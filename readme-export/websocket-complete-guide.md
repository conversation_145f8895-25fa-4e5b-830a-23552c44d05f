# WebSocket Complete Guide

StalkAPI provides real-time WebSocket streaming for live KOL (Key Opinion Leader) trading data. Get instant notifications when KOL traders make transactions.

## 🚀 Quick Start

### 1. Connect to WebSocket

```javascript
// Using JWT Token
const ws = new WebSocket('wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN');

// Using API Key  
const ws = new WebSocket('wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY');
```

### 2. Subscribe to KOL Feed

```javascript
ws.onopen = () => {
    console.log('Connected to StalkAPI WebSocket');
    
    // Subscribe to real-time KOL trading data
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: { stream: 'kol-feed' }
    }));
};

ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    
    if (message.type === 'stream_data' && message.stream === 'kol-feed') {
        console.log('KOL Trade:', message.data);
        // Process real-time trading data
    }
};
```

## 🔌 Connection Details

| Parameter | Value |
|-----------|-------|
| **URL** | `wss://data.stalkapi.com/ws` (prod)<br>`ws://localhost:3001/ws` (dev) |
| **Authentication** | JWT Token or API Key |
| **Protocol** | WebSocket (RFC 6455) |
| **Format** | JSON messages |

## 📡 Available Streams

| Stream | Description | Credits per Message | Tier Required |
|--------|-------------|-------------------|---------------|
| `kol-feed` | Real-time KOL trading activity | 2 | Basic+ |

## 💬 Message Protocol

### 📤 Client → Server Messages

#### Subscribe to Stream
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "kol-feed"
  }
}
```

#### Unsubscribe from Stream
```json
{
  "type": "unsubscribe",
  "payload": {
    "stream": "kol-feed"
  }
}
```

#### Keep Alive (Ping)
```json
{
  "type": "ping"
}
```

### 📥 Server → Client Messages

#### Connection Established
```json
{
  "type": "connected",
  "connectionId": "uuid-string",
  "sessionId": "uuid-string",
  "message": "WebSocket connection established"
}
```

#### Subscription Confirmed
```json
{
  "type": "subscribed",
  "stream": "kol-feed",
  "message": "Successfully subscribed to kol-feed"
}
```

#### KOL Trading Data
```json
{
  "type": "stream_data",
  "stream": "kol-feed",
  "data": {
    "timestamp": 1748923344035,
    "kol_label": "TraderSZ",
    "wallet": "private",
    "tokenIn": {
      "symbol": "SOL",
      "name": "Wrapped SOL",
      "amount": 13.0,
      "tokenInAmountUsd": 2095.16,
      "price": 161.17
    },
    "tokenOut": {
      "symbol": "BONK", 
      "name": "Bonk",
      "amount": 45200000,
      "tokenOutAmountUsd": 1987.45,
      "price": 0.000044
    },
    "transactionType": "buy",
    "chain": "solana"
  }
}
```

#### Error Messages
```json
{
  "type": "error",
  "error": "Access denied to stream: kol-feed",
  "timestamp": 1748926077000
}
```

#### Credit Warning
```json
{
  "type": "credit_warning",
  "message": "Insufficient credits for stream: kol-feed",
  "credits_remaining": 0,
  "credits_required": 2,
  "timestamp": 1748926077000
}
```

## 💻 Implementation Examples

### JavaScript/Node.js
```javascript
const WebSocket = require('ws');

// Connect with JWT token
const ws = new WebSocket('wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN');

ws.on('open', () => {
    console.log('Connected to StalkAPI WebSocket');
    
    // Subscribe to KOL feed
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: { stream: 'kol-feed' }
    }));
});

ws.on('message', (data) => {
    const message = JSON.parse(data);
    
    if (message.type === 'stream_data' && message.stream === 'kol-feed') {
        const trade = message.data;
        console.log(`KOL Trade: ${trade.kol_label} ${trade.transactionType} ` +
                   `${trade.tokenIn.symbol} -> ${trade.tokenOut.symbol} ` +
                   `($${trade.tokenInAmountUsd.toFixed(2)})`);
    }
    
    if (message.type === 'error') {
        console.error('WebSocket Error:', message.error);
    }
});

ws.on('close', () => {
    console.log('WebSocket connection closed');
});
```

### Python
```python
import asyncio
import websockets
import json

async def connect_to_stalkapi():
    uri = "wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN"
    
    async with websockets.connect(uri) as websocket:
        print("Connected to StalkAPI WebSocket")
        
        # Subscribe to KOL feed
        await websocket.send(json.dumps({
            "type": "subscribe",
            "payload": {"stream": "kol-feed"}
        }))
        
        async for message in websocket:
            data = json.loads(message)
            
            if data["type"] == "stream_data" and data["stream"] == "kol-feed":
                trade = data["data"]
                print(f"KOL Trade: {trade['kol_label']} {trade['transactionType']} "
                      f"{trade['tokenIn']['symbol']} -> {trade['tokenOut']['symbol']} "
                      f"(${trade['tokenInAmountUsd']:.2f})")
            
            elif data["type"] == "error":
                print(f"Error: {data['error']}")

# Run the WebSocket client
asyncio.run(connect_to_stalkapi())
```

## 🔧 Testing WebSocket Connection

Use `wscat` for quick testing:
```bash
# Install wscat
npm install -g wscat

# Connect and test
wscat -c "wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN"

# Send subscription message
{"type": "subscribe", "payload": {"stream": "kol-feed"}}
```

## 💳 Credit System

- **KOL Feed Stream**: 2 credits per message
- **Minimum Tier**: Basic tier or higher
- **Rate Limiting**: Based on your tier limits

## 🚨 Error Handling

### Common Errors

#### Authentication Failed
```json
{"type": "error", "error": "Authentication failed"}
```
**Solution**: Check your JWT token or API key validity

#### Access Denied to Stream
```json
{"type": "error", "error": "Access denied to stream: kol-feed"}
```
**Solution**: Verify your tier has access to the requested stream

#### Insufficient Credits
```json
{"type": "credit_warning", "message": "Insufficient credits for stream: kol-feed"}
```
**Solution**: Upgrade your tier or wait for credit renewal

## 🔄 Connection Management

### Reconnection Logic
```javascript
function connectWithRetry() {
    const ws = new WebSocket('wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN');
    
    ws.onclose = () => {
        console.log('Connection lost, reconnecting in 5 seconds...');
        setTimeout(connectWithRetry, 5000);
    };
    
    ws.onerror = (error) => {
        console.error('WebSocket error:', error);
    };
}
```

### Heartbeat/Ping
```javascript
setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }));
    }
}, 30000); // Ping every 30 seconds
```

## 📊 Rate Limits

| Tier | Max Connections | Messages/Minute |
|------|----------------|-----------------|
| Free | 1 | 30 |
| Basic | 3 | 100 |
| Premium | 10 | 500 |
| Enterprise | 50 | Unlimited |

## 🔗 Related Endpoints

- [WebSocket Connection Info](/reference/get_ws-api-info) - Get connection details
- [Available Streams](/reference/get_ws-api-streams) - List available streams
- [Active Sessions](/reference/get_ws-api-sessions) - Manage active connections
- [Usage Statistics](/reference/get_ws-api-stats) - View WebSocket usage

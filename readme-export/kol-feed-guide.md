# KOL Feed Integration Guide

## Overview

The KOL Feed provides real-time trading activity from Key Opinion Leaders (KOLs) in the cryptocurrency space. This feature delivers live transaction data including trader information, token swaps, transaction amounts, and social profiles.

## Features

- **Real-time KOL Trading Data**: Live transaction feeds from verified KOL traders
- **Historical Data Access**: REST API endpoint for the last 100 transactions
- **Privacy Protection**: Private wallets show "private" instead of actual addresses
- **Rich Metadata**: Complete token information, social profiles, and transaction details
- **Credit-based Access**: 2 credits per message received, 3 credits per history request
- **Event-driven Architecture**: Uses Redis pub/sub for scalable message distribution
- **Redis-based Storage**: Maintains last 100 transactions for quick historical access
- **Automatic Reconnection**: Robust WebSocket connection with auto-reconnect

## Architecture

### Components

1. **KolFeed Worker** (`src/workers/kolFeed.js`)
   - Connects to external KOL data WebSocket
   - Transforms raw data to API format
   - Publishes to internal Redis channel

2. **Stream Manager Integration**
   - Subscribes to internal Redis channel
   - Broadcasts to WebSocket clients
   - Handles credit deduction per message

3. **Stream Credit System**
   - Checks user credits before message delivery
   - Deducts 2 credits per message received
   - Sends credit warnings for insufficient credits

### Data Flow

```
External KOL WebSocket → KolFeed Worker → Redis Pub/Sub → Stream Manager → WebSocket Clients
                                      ↓
                                 Redis List Storage (last 100) → REST API History Endpoint
```

## Configuration

### Environment Variables

```bash
# Required for KOL Feed
STALKCHAIN_CENTRAL_WSS_URL=wss://your-kol-feed-source.com
STALKCHAIN_CENTRAL_KEY=your-api-key

# Stream Credits (optional)
STREAM_CREDITS_ENABLED=true
```

### Database Setup

The KOL feed stream is automatically configured in the database:

```sql
-- KOL feed stream definition
INSERT INTO stream_definitions (
    stream_name,
    description,
    required_tier_id,
    credits_per_message,
    max_subscribers,
    is_active
) VALUES (
    'kol-feed',
    'Real-time KOL (Key Opinion Leader) trading activity feed',
    2, -- basic tier
    2, -- 2 credits per message
    500, -- max subscribers
    true
);
```

## Usage

### WebSocket Connection

Connect to the WebSocket server and subscribe to the KOL feed:

```javascript
const ws = new WebSocket('ws://localhost:3001/ws?token=YOUR_JWT_TOKEN');

ws.onopen = () => {
    // Subscribe to KOL feed
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: {
            stream: 'kol-feed'
        }
    }));
};

ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    
    if (message.type === 'stream_data' && message.stream === 'kol-feed') {
        console.log('KOL Trade:', message.data);
    }
    
    if (message.type === 'credit_warning') {
        console.log('Insufficient credits for KOL feed');
    }
};
```

### Historical Data API

Access the last 100 KOL transactions via REST API:

```bash
# Get last 10 transactions
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3001/api/v1/kol-feed/history?limit=10"

# Get transactions with pagination
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3001/api/v1/kol-feed/history?limit=20&offset=10"

# Using API key authentication
curl -H "X-API-Key: YOUR_API_KEY" \
  "http://localhost:3001/api/v1/kol-feed/history?limit=50"
```

**Response Format:**
```json
{
  "success": true,
  "data": [
    {
      "timestamp": 1748919450434,
      "kol_label": "TraderSZ",
      "wallet": "private",
      // ... same format as WebSocket messages
    }
  ],
  "pagination": {
    "limit": 10,
    "offset": 0,
    "total": 45,
    "returned": 10
  },
  "credits_consumed": 3,
  "message": "KOL feed history retrieved successfully"
}
```

### Message Format

KOL feed messages follow this structure:

```json
{
  "type": "stream_data",
  "stream": "kol-feed",
  "data": {
    "timestamp": 1748919450434,
    "kol_label": "TraderSZ",
    "wallet": "private",
    "kol_avatar": "https://example.com/avatar.jpg",
    "tokenIn": {
      "symbol": "SOL",
      "name": "Wrapped SOL",
      "logo": "https://example.com/sol-logo.png",
      "tokenAmountString": "13.0",
      "amount": 13.0,
      "tokenInAmountUsd": 2095.16,
      "price": 161.17,
      "mint": "So11111111111111111111111111111111111111112"
    },
    "tokenOut": {
      "symbol": "USDUC",
      "name": "unstable coin",
      "logo": "https://example.com/usduc-logo.png",
      "tokenAmountString": "294.12K",
      "amount": 294116.82,
      "tokenOutAmountUsd": 2041.57,
      "price": 0.006941,
      "mint": "CB9dDufT3ZuQXqqSfa1c5kY935TEreyBw9XJXxHKpump"
    },
    "signature": "private",
    "transactionType": "buy",
    "chain": "solana",
    "socials": [
      {
        "type": "x",
        "handle": "trader1sz",
        "followers": 658700
      }
    ]
  },
  "timestamp": 1748919450434
}
```

### Data Fields

| Field | Type | Description |
|-------|------|-------------|
| `timestamp` | number | Unix timestamp of the transaction |
| `kol_label` | string | KOL trader display name |
| `wallet` | string | Wallet address (or "private" for private wallets) |
| `kol_avatar` | string | URL to KOL avatar image |
| `tokenIn` | object | Token being sold/swapped from |
| `tokenOut` | object | Token being bought/swapped to |
| `signature` | string | Transaction signature (or "private") |
| `transactionType` | string | "buy" or "sell" |
| `chain` | string | Blockchain network (e.g., "solana") |
| `socials` | array | KOL social media profiles |

### Token Object Structure

```json
{
  "symbol": "SOL",
  "name": "Wrapped SOL",
  "logo": "https://example.com/logo.png",
  "tokenAmountString": "13.0",
  "amount": 13.0,
  "tokenInAmountUsd": 2095.16,
  "price": 161.17,
  "mint": "So11111111111111111111111111111111111111112"
}
```

## Credit Management

### Credit Costs

- **WebSocket Connection**: 0 credits
- **WebSocket Subscription**: 0 credits
- **WebSocket Message Received**: 2 credits per message
- **REST API History Request**: 3 credits per request

### Credit Checking

Check your credit status and KOL feed costs:

```bash
# Get general credit info
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3001/ws-api/credits"

# Get KOL feed specific info
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3001/ws-api/credits?stream=kol-feed"

# Get usage statistics
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3001/ws-api/credits/stats?stream=kol-feed&days=7"
```

### Credit Warnings

When you don't have sufficient credits, you'll receive warning messages:

```json
{
  "type": "credit_warning",
  "stream": "kol-feed",
  "message": "Insufficient credits to receive stream data",
  "required_credits": 2,
  "timestamp": 1748919450434
}
```

## Access Requirements

### Tier Requirements

- **Minimum Tier**: Basic (tier_id: 2)
- **Credits Required**: 2 credits per message
- **Max Subscribers**: 500 concurrent users

### Tier Access

| Tier | Access | Credits/Month | Notes |
|------|--------|---------------|-------|
| Free | ❌ No | 1,000 | Tier disabled by default |
| Basic | ✅ Yes | 10,000 | 5,000 messages max |
| Premium | ✅ Yes | 100,000 | 50,000 messages max |
| Enterprise | ✅ Yes | Unlimited | No message limits |

## Implementation Details

### KolFeed Worker

The KolFeed worker handles the external WebSocket connection:

```javascript
// src/workers/kolFeed.js
export class KolFeed {
  constructor() {
    this.isRunning = false;
    this.ws = null;
  }

  async init() {
    this.isRunning = true;
    this.connect();
  }

  connect() {
    this.ws = new WebSocket(WS_URL);
    
    this.ws.on('message', (message) => {
      const json = JSON.parse(message);
      if (json?.tx && json?.source === 'kol' && json?.tx?.isKol) {
        const transformedData = transformKolFeedData(json);
        pubsub.publish('kol_feed_internal', transformedData);
      }
    });
  }
}
```

### Data Transformation

Raw KOL data is transformed to match the API format:

```javascript
function transformKolFeedData(json) {
  const tx = json.tx;
  return {
    timestamp: json.timestamp,
    kol_label: tx.wallet_label,
    wallet: tx.isPublic ? tx.walletAddress : "private",
    kol_avatar: tx.wallet_avatar,
    tokenIn: {
      symbol: tx.tokenIn?.symbol,
      name: tx.tokenIn?.name,
      // ... other fields
    },
    // ... rest of transformation
  };
}
```

## Monitoring and Debugging

### Logs

Monitor KOL feed activity in the server logs:

```bash
# KOL feed connection
[KOLFeed] Connected to WebSocket

# Message processing
✅ [KOLFeed] Published message for TraderSZ
📨 Received KOL feed internal message

# Credit management
💳 1/1 users have sufficient credits for kol-feed
💳 Consumed 2 credits for user on stream kol-feed
```

### Health Checks

Check if the KOL feed is working:

```bash
# Check stream status
curl "http://localhost:3001/ws-api/streams" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Check active sessions
curl "http://localhost:3001/ws-api/sessions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Troubleshooting

### Common Issues

1. **No KOL feed messages**
   - Check `STALKCHAIN_CENTRAL_WSS_URL` and `STALKCHAIN_CENTRAL_KEY`
   - Verify external WebSocket connection in logs
   - Check if KOL feed worker is running

2. **Credit warnings**
   - Check user credit balance: `GET /auth/profile`
   - Verify stream credit cost: `GET /ws-api/credits?stream=kol-feed`
   - Consider upgrading user tier

3. **Connection issues**
   - Check WebSocket authentication
   - Verify user has Basic+ tier access
   - Check rate limiting and connection limits

### Debug Commands

```sql
-- Check KOL feed stream configuration
SELECT * FROM stream_definitions WHERE stream_name = 'kol-feed';

-- Check user access to KOL feed
SELECT u.email, u.credits_remaining, at.name as tier, at.allowed_streams
FROM users u
JOIN access_tiers at ON u.tier_id = at.id
WHERE u.email = '<EMAIL>';

-- Check recent KOL feed usage
SELECT * FROM api_usage_logs
WHERE endpoint = 'websocket:/kol-feed'
ORDER BY created_at DESC
LIMIT 10;
```

This KOL feed integration provides a robust, scalable solution for delivering real-time trading data with proper credit management and access controls.

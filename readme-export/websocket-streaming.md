# WebSocket Streaming

StalkAPI provides real-time WebSocket streaming for live KOL (Key Opinion Leader) trading data. Get instant notifications when KOL traders make transactions.

## Quick Start

### 1. Connect to WebSocket

```javascript
// Using JWT Token
const ws = new WebSocket('ws://localhost:3001/ws?token=YOUR_JWT_TOKEN');

// Using API Key  
const ws = new WebSocket('ws://localhost:3001/ws?apiKey=YOUR_API_KEY');
```

### 2. Subscribe to KOL Feed

```javascript
ws.onopen = () => {
    // Subscribe to real-time KOL trading data
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: { stream: 'kol-feed' }
    }));
};
```

### 3. Handle Stream Data

```javascript
ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    
    if (message.type === 'stream_data' && message.stream === 'kol-feed') {
        const trade = message.data;
        console.log(`${trade.kol_label} ${trade.transactionType} ${trade.tokenIn.symbol} -> ${trade.tokenOut.symbol}`);
    }
};
```

## Connection Details

| Parameter | Value |
|-----------|-------|
| **URL** | `ws://localhost:3001/ws` (dev)<br>`wss://your-domain.com/ws` (prod) |
| **Authentication** | JWT Token or API Key |
| **Protocol** | WebSocket (RFC 6455) |
| **Format** | JSON messages |

## Available Streams

| Stream | Description | Credits per Message | Tier Required |
|--------|-------------|-------------------|---------------|
| `kol-feed` | Real-time KOL trading activity | 2 | Basic+ |

## Message Types

### Client → Server

#### Subscribe to Stream
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "kol-feed"
  }
}
```

#### Unsubscribe from Stream
```json
{
  "type": "unsubscribe",
  "payload": {
    "stream": "kol-feed"
  }
}
```

#### Keep Alive (Ping)
```json
{
  "type": "ping"
}
```

### Server → Client

#### Connection Established
```json
{
  "type": "connected",
  "connectionId": "uuid-string",
  "message": "WebSocket connection established"
}
```

#### Subscription Confirmed
```json
{
  "type": "subscribed",
  "stream": "kol-feed",
  "message": "Successfully subscribed to kol-feed"
}
```

#### KOL Trading Data
```json
{
  "type": "stream_data",
  "stream": "kol-feed",
  "data": {
    "timestamp": 1748923344035,
    "kol_label": "TraderSZ",
    "wallet": "private",
    "tokenIn": {
      "symbol": "SOL",
      "name": "Wrapped SOL",
      "amount": 13.0,
      "tokenInAmountUsd": 2095.16,
      "price": 161.17
    },
    "tokenOut": {
      "symbol": "BONK", 
      "name": "Bonk",
      "amount": 45200000,
      "tokenOutAmountUsd": 1987.45,
      "price": 0.000044
    },
    "transactionType": "buy",
    "chain": "solana"
  }
}
```

#### Error Messages
```json
{
  "type": "error",
  "error": "Access denied to stream: kol-feed"
}
```

#### Credit Warning
```json
{
  "type": "credit_warning",
  "message": "Insufficient credits for stream: kol-feed",
  "credits_remaining": 0,
  "credits_required": 2
}
```

## Implementation Examples

### JavaScript/Browser
```javascript
class StalkAPIWebSocket {
    constructor(token) {
        this.ws = new WebSocket(`ws://localhost:3001/ws?token=${token}`);
        this.setupEventHandlers();
    }
    
    setupEventHandlers() {
        this.ws.onopen = () => {
            console.log('Connected to StalkAPI');
            this.subscribeToKOLFeed();
        };
        
        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
        
        this.ws.onclose = () => {
            console.log('Connection closed');
            // Implement reconnection logic here
        };
    }
    
    subscribeToKOLFeed() {
        this.ws.send(JSON.stringify({
            type: 'subscribe',
            payload: { stream: 'kol-feed' }
        }));
    }
    
    handleMessage(message) {
        switch (message.type) {
            case 'stream_data':
                if (message.stream === 'kol-feed') {
                    this.onKOLTrade(message.data);
                }
                break;
            case 'error':
                console.error('Stream error:', message.error);
                break;
            case 'credit_warning':
                console.warn('Credit warning:', message.message);
                break;
        }
    }
    
    onKOLTrade(trade) {
        console.log('New KOL Trade:', {
            trader: trade.kol_label,
            type: trade.transactionType,
            from: trade.tokenIn.symbol,
            to: trade.tokenOut.symbol,
            amount: `$${trade.tokenInAmountUsd.toFixed(2)}`
        });
    }
}

// Usage
const stalkAPI = new StalkAPIWebSocket('YOUR_JWT_TOKEN');
```

### Node.js
```javascript
const WebSocket = require('ws');

class StalkAPIClient {
    constructor(token) {
        this.token = token;
        this.connect();
    }
    
    connect() {
        this.ws = new WebSocket(`ws://localhost:3001/ws?token=${this.token}`);
        
        this.ws.on('open', () => {
            console.log('Connected to StalkAPI');
            this.subscribe('kol-feed');
            this.startHeartbeat();
        });
        
        this.ws.on('message', (data) => {
            const message = JSON.parse(data.toString());
            this.handleMessage(message);
        });
        
        this.ws.on('close', () => {
            console.log('Connection closed, reconnecting...');
            setTimeout(() => this.connect(), 5000);
        });
    }
    
    subscribe(stream) {
        this.ws.send(JSON.stringify({
            type: 'subscribe',
            payload: { stream }
        }));
    }
    
    startHeartbeat() {
        setInterval(() => {
            if (this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ type: 'ping' }));
            }
        }, 30000);
    }
    
    handleMessage(message) {
        if (message.type === 'stream_data' && message.stream === 'kol-feed') {
            console.log('KOL Trade:', message.data);
        }
    }
}

const client = new StalkAPIClient('YOUR_JWT_TOKEN');
```

### Python
```python
import asyncio
import websockets
import json

class StalkAPIClient:
    def __init__(self, token):
        self.token = token
        self.uri = f"ws://localhost:3001/ws?token={token}"
    
    async def connect(self):
        async with websockets.connect(self.uri) as websocket:
            print("Connected to StalkAPI")
            
            # Subscribe to KOL feed
            await websocket.send(json.dumps({
                "type": "subscribe",
                "payload": {"stream": "kol-feed"}
            }))
            
            # Listen for messages
            async for message in websocket:
                data = json.loads(message)
                await self.handle_message(data)
    
    async def handle_message(self, message):
        if message["type"] == "stream_data" and message["stream"] == "kol-feed":
            trade = message["data"]
            print(f"KOL Trade: {trade['kol_label']} {trade['transactionType']} "
                  f"{trade['tokenIn']['symbol']} -> {trade['tokenOut']['symbol']} "
                  f"(${trade['tokenInAmountUsd']:.2f})")

# Usage
client = StalkAPIClient("YOUR_JWT_TOKEN")
asyncio.run(client.connect())
```

## Credit Management

### Credit Costs
- **Connection**: Free (no credits consumed)
- **KOL Feed Messages**: 2 credits per message

### Monitoring Credits
Check your credit balance regularly:
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3001/auth/profile"
```

### Insufficient Credits
When credits run low, you'll receive warning messages instead of stream data:
```json
{
  "type": "credit_warning",
  "message": "Insufficient credits for stream: kol-feed",
  "credits_remaining": 0,
  "credits_required": 2
}
```

## Best Practices

### 1. Connection Management
- Implement automatic reconnection with exponential backoff
- Use ping/pong for keep-alive (every 30 seconds)
- Handle connection errors gracefully

### 2. Error Handling
- Always parse JSON in try-catch blocks
- Log important events for debugging
- Implement proper error recovery

### 3. Performance
- Process messages asynchronously
- Avoid blocking the message handler
- Consider message queuing for high-volume scenarios

### 4. Security
- Use WSS (secure WebSocket) in production
- Validate all incoming messages
- Rotate authentication tokens regularly

## Rate Limits

| Tier | Max Connections | Messages/Second |
|------|----------------|-----------------|
| Basic | 3 | Unlimited |
| Premium | 10 | Unlimited |
| Enterprise | 50 | Unlimited |

## Troubleshooting

### Common Issues

**Authentication Failed**
```json
{"type": "error", "error": "Authentication failed"}
```
*Solution*: Verify your JWT token or API key is valid

**Access Denied**
```json
{"type": "error", "error": "Access denied to stream: kol-feed"}
```
*Solution*: Check your tier has access to the requested stream

**Connection Drops**
*Solution*: Implement reconnection logic with exponential backoff

### Testing Connection
Use `wscat` for quick testing:
```bash
npm install -g wscat
wscat -c "ws://localhost:3001/ws?token=YOUR_JWT_TOKEN"
```

## WebSocket API Endpoints

Get WebSocket server information:
```bash
GET /ws-api/info
Authorization: Bearer YOUR_JWT_TOKEN
```

List available streams:
```bash
GET /ws-api/streams  
Authorization: Bearer YOUR_JWT_TOKEN
```

Get stream credit costs:
```bash
GET /ws-api/credits?stream=kol-feed
Authorization: Bearer YOUR_JWT_TOKEN
```

---

**Ready to start?** Get your authentication token from the [Authentication](/docs/authentication) section and connect to the live KOL feed!

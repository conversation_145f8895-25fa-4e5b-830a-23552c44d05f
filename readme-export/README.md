# Readme.com Documentation Export

This folder contains all documentation files ready for upload to Readme.com.

## Files Included

- `getting-started.md` - Getting Started guide
- `credit-system.md` - Credit System documentation
- `kol-feed-guide.md` - KOL Feed guide
- `kol-feed-history-api.md` - KOL Feed History API
- `websocket-streaming.md` - WebSocket streaming guide and examples
- `postman-guide.md` - Postman collection guide
- `api-documentation.md` - General API documentation
- `openapi.yaml` - OpenAPI specification

## Manual Upload Instructions

### For Git-backed Readme.com projects:

1. **OpenAPI Specification:**
   - Go to your Readme.com dashboard
   - Navigate to "API Reference" section
   - Upload `openapi.yaml` file
   - Or use: `rdme openapi openapi.yaml --key=YOUR_API_KEY`

2. **Documentation Pages:**
   - Go to your Readme.com dashboard
   - Create new pages in appropriate categories
   - Copy content from markdown files
   - Organize into categories:
     - Getting Started → `getting-started.md`
     - Core Concepts → `credit-system.md`
     - Features → `kol-feed-guide.md`
     - API Reference → `kol-feed-history-api.md`
     - Tools & SDKs → `postman-guide.md`

3. **Using rdme CLI:**
   ```bash
   # Install rdme CLI
   npm install -g rdme
   
   # Sync OpenAPI spec
   rdme openapi openapi.yaml --key=YOUR_API_KEY
   
   # Sync individual docs (if supported)
   rdme docs getting-started.md --key=YOUR_API_KEY
   ```

## Project Information

- **Project URL:** https://stalkapi.readme.io
- **API Reference:** https://stalkapi.readme.io/reference
- **Project Slug:** stalkapi

Generated on: 2025-06-03T05:13:34.442Z

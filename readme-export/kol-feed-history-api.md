# KOL Feed History API

## Overview

The KOL Feed History API provides access to the last 100 KOL (Key Opinion Leader) trading transactions that have been processed through the real-time KOL feed. This endpoint allows users to retrieve historical data for analysis and backtesting purposes.

## Endpoint

```
GET /api/v1/kol-feed/history
```

## Authentication

- **JWT Token**: Include in Authorization header as `Bearer <token>`
- **API Key**: Include in X-API-Key header

## Access Requirements

- **Minimum Tier**: Basic tier or higher
- **Credits Required**: 3 credits per request
- **Permissions**: User must have access to `/api/v1/kol-feed/history` endpoint

## Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `limit` | integer | 100 | Number of messages to return (max: 100) |
| `offset` | integer | 0 | Number of messages to skip (for pagination) |

## Response Format

```json
{
  "success": true,
  "data": [
    {
      "timestamp": 1748913955272,
      "kol_label": "Jidn",
      "wallet": "********************************************",
      "kol_avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/jidn_w.jpg",
      "tokenIn": {
        "symbol": "SOL",
        "name": "Wrapped SOL",
        "logo": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",
        "tokenAmountString": "0.92",
        "amount": 0.9197152919999994,
        "tokenInAmountUsd": 146.465609,
        "price": 159.251032,
        "mint": "So11111111111111111111111111111111111111112"
      },
      "tokenOut": {
        "symbol": "CAREBUBU",
        "name": "NEW CAREBEAR LABUBU DOLLS",
        "logo": "",
        "tokenAmountString": "16.71M",
        "amount": 16708878.061636,
        "tokenOutAmountUsd": 145.000953,
        "price": 0.000009,
        "mint": "HRum1AZkwkG35nFBZavpHuadZHXVLss1bN8jp64Jpump"
      },
      "signature": "5uTz1FZkUiZpe418qEUJ5a9w7bsnbhDNw7N4XouW9ufsTxBBQCE1FYqeipzjpcXqwWrH7Nji32hccBwso4NbErTr",
      "transactionType": "buy",
      "chain": "solana",
      "socials": [...]
    }
  ],
  "pagination": {
    "limit": 100,
    "offset": 0,
    "total": 45,
    "returned": 45
  },
  "credits_consumed": 3,
  "message": "KOL feed history retrieved successfully"
}
```

## Data Storage

- **Storage**: Redis list with key `kol_feed_history`
- **Retention**: Last 100 messages (FIFO - First In, First Out)
- **Update Frequency**: Real-time as new KOL transactions are processed

## Example Requests

### Using cURL with JWT Token

```bash
curl -X GET "https://your-api.com/api/v1/kol-feed/history?limit=10&offset=0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Using cURL with API Key

```bash
curl -X GET "https://your-api.com/api/v1/kol-feed/history?limit=10&offset=0" \
  -H "X-API-Key: YOUR_API_KEY"
```

### JavaScript/Node.js Example

```javascript
const response = await fetch('/api/v1/kol-feed/history?limit=50', {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log('KOL History:', data.data);
```

## Error Responses

### Insufficient Credits (402)
```json
{
  "error": "Insufficient credits",
  "credits_remaining": 0,
  "credits_required": 3
}
```

### Access Denied (403)
```json
{
  "error": "Access denied - insufficient tier permissions",
  "required_tier": "Higher tier required for this endpoint"
}
```

### Server Error (500)
```json
{
  "error": "Failed to retrieve KOL feed history"
}
```

## Rate Limiting

- Follows standard API rate limits based on user tier
- Basic tier: 60 requests per minute
- Premium tier: 300 requests per minute
- Enterprise tier: Custom limits

## Use Cases

1. **Historical Analysis**: Analyze past KOL trading patterns
2. **Backtesting**: Test trading strategies against historical KOL data
3. **Research**: Study KOL behavior and market impact
4. **Dashboard Integration**: Display recent KOL activity in applications

## Notes

- Data is stored in memory (Redis) and will be lost on server restart
- For persistent historical data, consider implementing database storage
- Messages are ordered by most recent first (newest at index 0)
- Private transactions show "private" for wallet addresses and signatures

# StalkAPI Postman Collection Guide

This guide explains how to use the StalkAPI Postman collection to test and interact with the KOL feed API, including REST endpoints and WebSocket connection information.

## 📦 Collection Files

Available for download:
- **`StalkAPI_Collection.json`** - Clean KOL feed API collection
- **Environment variables** - Pre-configured for development and production

## 🚀 Quick Setup

### 1. Import Collection and Environment

1. Open Postman
2. Click **Import** button
3. Import both files from the `postman/` folder:
   - `StalkAPI_Postman_Collection.json` (REST API)
   - `StalkAPI_Postman_Environment.json` (Environment)

### 2. Select Environment

1. In the top-right corner, select **"StalkAPI Environment"**
2. Verify the `base_url` is set correctly:
   - Development: `http://localhost:3001`
   - Production: `https://data.stalkapi.com`

### 3. Start Testing

1. Run the **"Login"** request first to get a JWT token
2. The token will be automatically saved to environment variables
3. All other requests will use this token automatically

## 📋 Collection Structure

### 🏥 System Health

- **Health Check** - Test API availability (no auth required)

### 🔐 Authentication

- **Login** - Get JWT token (auto-saves to environment)
- **Get Profile** - View user information and credits

### 📊 KOL Feed API

- **KOL Feed History (API Key)** - Historical KOL trading data using API key (3 credits)
- **KOL Feed History (JWT)** - Historical KOL trading data using JWT token (3 credits)

### 🔌 WebSocket API

- **WebSocket Info** - Get connection details and status
- **Available Streams** - List streams available for user's tier
- **Stream Credits Info** - Get credit costs for WebSocket streams

## 🎯 Demo Credentials

The collection comes pre-configured with demo user credentials:

```
Email: <EMAIL>
Password: demo123
API Key: demo_api_key_12345
Tier: Basic (10,000 credits)
```

## 🔄 Workflow Examples

### Basic API Testing Workflow

1. **Health Check** → Verify API is running
2. **Login** → Get JWT token
3. **Get Profile** → Check credits and tier
4. **KOL Feed History** → Test KOL data access
5. **Get Profile** → Verify credit consumption

### WebSocket Testing Workflow

1. **Login** → Get JWT token
2. **WebSocket Info** → Get connection details
3. **Available Streams** → See available streams (should show kol-feed)
4. **Stream Credits Info** → Check credit costs
5. Use WebSocket client with: `ws://localhost:3001/ws?token={{jwt_token}}`

### Authentication Testing Workflow

1. **Login** → Get JWT token
2. **KOL Feed History (JWT)** → Test JWT authentication
3. **KOL Feed History (API Key)** → Test API key authentication
4. Compare responses and functionality

## 🌍 Environment Variables

### Automatic Variables

- `jwt_token` - Set automatically after login
- `user_id` - Set automatically after login

### Manual Variables

- `base_url` - API base URL
- `api_key` - Demo API key
- `websocket_url` - WebSocket connection URL

### Production Setup

To test against production:

1. Change `base_url` to `https://data.stalkapi.com`
2. Change `websocket_url` to `wss://data.stalkapi.com/ws`

## 📊 Understanding Responses

### Successful API Response

```json
{
  "success": true,
  "data": { ... },
  "credits_remaining": 9999,
  "credits_used": 1
}
```

### Error Response

```json
{
  "error": "Insufficient credits",
  "code": "INSUFFICIENT_CREDITS",
  "credits_remaining": 0
}
```

### Authentication Response

```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "tier": "basic",
    "credits_remaining": 10000
  }
}
```

## 🔧 Testing Different Scenarios

### Credit Consumption Testing

1. Check initial credits with **Get Profile**
2. Make API calls and watch credits decrease
3. Verify credit consumption matches endpoint costs

### Tier Access Testing

1. Try **Analytics Endpoint** with Basic tier (should fail)
2. Try **Demo Endpoint** with any tier (should work)
3. Check error messages for access denied

### Rate Limiting Testing

1. Make rapid requests to same endpoint
2. Observe rate limiting responses (429 status)
3. Wait and try again

### API Key vs JWT Testing

1. Use JWT auth endpoints
2. Use API key auth endpoints
3. Compare functionality and responses

## 🐛 Troubleshooting

### Common Issues

**"Unauthorized" Error**

- Check if JWT token is set in environment
- Try logging in again to refresh token
- Verify API key is correct for API key endpoints

**"Insufficient Credits" Error**

- Check credits with **Get Profile**
- This is expected behavior when credits run out

**"Access Denied" Error**

- Check user's tier with **Get Profile**
- Verify endpoint is available for user's tier

**Connection Refused**

- Ensure API server is running on correct port
- Check `base_url` in environment variables

### Debug Tips

1. **Check Environment Variables**

   - Click the eye icon next to environment dropdown
   - Verify all variables are set correctly

2. **View Request Details**

   - Check Headers tab for Authorization header
   - Verify request body format for POST requests

3. **Check Response**
   - Look at response status code
   - Read error messages in response body

## 📝 Custom Testing

### Adding New Requests

1. Right-click on folder → Add Request
2. Set method, URL, headers, and body
3. Use environment variables: `{{variable_name}}`

### Creating Test Scripts

Add to Tests tab:

```javascript
pm.test("Status code is 200", function () {
  pm.response.to.have.status(200);
});

pm.test("Response has success field", function () {
  const response = pm.response.json();
  pm.expect(response).to.have.property("success");
});
```

## 🔗 WebSocket Testing

For WebSocket testing, use external tools:

### Using wscat (Node.js)

```bash
npm install -g wscat
wscat -c "ws://localhost:3001/ws?token=YOUR_JWT_TOKEN"
```

### WebSocket Messages

```json
// Subscribe to KOL feed
{
    "type": "subscribe",
    "payload": {
        "stream": "kol-feed"
    }
}

// Unsubscribe from KOL feed
{
    "type": "unsubscribe",
    "payload": {
        "stream": "kol-feed"
    }
}

// Ping (keep alive)
{"type": "ping"}
```

## 📱 Collection Features

This collection provides comprehensive testing capabilities for:

- **KOL Feed API**: Historical trading data access
- **WebSocket Streaming**: Real-time KOL trading activity
- **Authentication**: Both JWT and API key methods
- **Credit Management**: Track usage and consumption
- **Tier-based Access**: Test access control

## 🔗 Related Documentation

- [WebSocket Streaming Guide](/docs/websocket-streaming) - Complete WebSocket documentation
- [KOL Feed Guide](/docs/kol-feed-guide) - KOL feed integration guide
- [Authentication](/docs/authentication) - Authentication methods
- [Credit System](/docs/credit-system) - Credit management

Start testing your KOL feed integration today!

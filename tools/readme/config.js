/**
 * Readme.com API Configuration
 * Centralized configuration for all Readme.com tools
 */

import dotenv from 'dotenv';
dotenv.config();

// Readme.com API Configuration
export const README_CONFIG = {
    apiKey: process.env.README_API_KEY,
    projectSlug: process.env.README_PROJECT_SLUG || 'stalkapi',
    version: process.env.README_VERSION || 'v1.0',
    baseUrl: 'https://dash.readme.com/api/v1',
    customDomain: process.env.README_CUSTOM_DOMAIN || null
};

// API Endpoints
export const README_ENDPOINTS = {
    docs: '/docs',
    categories: '/categories',
    apiSpec: '/api-specification',
    versions: '/version',
    changelogs: '/changelogs',
    customPages: '/custompages'
};

// Authentication headers
export const getAuthHeaders = () => ({
    'Authorization': `Basic ${Buffer.from(README_CONFIG.apiKey + ':').toString('base64')}`,
    'Content-Type': 'application/json',
    'User-Agent': 'StalkAPI-Documentation-Tools/1.0'
});

// Validation
export const validateConfig = () => {
    if (!README_CONFIG.apiKey) {
        throw new Error('README_API_KEY environment variable is required');
    }
    
    if (!README_CONFIG.apiKey.startsWith('rdme_')) {
        throw new Error('Invalid README_API_KEY format. Should start with "rdme_"');
    }
    
    return true;
};

// Helper function to make API requests
export const makeReadmeRequest = async (endpoint, options = {}) => {
    validateConfig();
    
    const url = `${README_CONFIG.baseUrl}${endpoint}`;
    const defaultOptions = {
        headers: getAuthHeaders()
    };
    
    const requestOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(url, requestOptions);
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Readme.com API Error (${response.status}): ${errorText}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error(`Error making request to ${url}:`, error.message);
        throw error;
    }
};

// Project information
export const PROJECT_INFO = {
    name: 'StalkAPI',
    description: 'Production-ready API engine with credit-based usage tracking and real-time WebSocket streaming',
    url: `https://${README_CONFIG.projectSlug}.readme.io`,
    customUrl: README_CONFIG.customDomain ? `https://${README_CONFIG.customDomain}` : null
};

export default {
    README_CONFIG,
    README_ENDPOINTS,
    getAuthHeaders,
    validateConfig,
    makeReadmeRequest,
    PROJECT_INFO
};

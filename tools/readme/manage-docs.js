#!/usr/bin/env node

/**
 * Manage Documentation Pages on Readme.com
 * Create, update, delete, and list documentation pages
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { makeReadmeRequest, README_ENDPOINTS, PROJECT_INFO } from './config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Documentation pages configuration
const DOC_PAGES = [
    {
        slug: 'getting-started',
        title: 'Getting Started',
        category: 'Getting Started',
        file: '../../docs/SETUP_GUIDE.md',
        type: 'basic'
    },
    {
        slug: 'credit-system',
        title: 'Credit System',
        category: 'Core Concepts',
        file: '../../docs/CREDIT_SYSTEM_GUIDE.md',
        type: 'basic'
    },
    {
        slug: 'kol-feed-guide',
        title: 'KOL Feed Guide',
        category: 'Features',
        file: '../../docs/KOL_FEED_GUIDE.md',
        type: 'basic'
    },
    {
        slug: 'kol-feed-history-api',
        title: 'KOL Feed History API',
        category: 'API Reference',
        file: '../../docs/KOL_FEED_HISTORY_API.md',
        type: 'basic'
    },
    {
        slug: 'postman-guide',
        title: 'Postman Collection',
        category: 'Tools & SDKs',
        file: '../../docs/POSTMAN_GUIDE.md',
        type: 'basic'
    }
];

async function listDocuments() {
    try {
        console.log('📋 Fetching all documentation pages...');

        const response = await makeReadmeRequest(README_ENDPOINTS.docs, {
            method: 'GET'
        });

        console.log(`✅ Found ${response.length} documentation pages:`);
        response.forEach(doc => {
            console.log(`  📄 ${doc.title} (${doc.slug}) - ${doc.category?.title || 'No category'}`);
        });

        return response;

    } catch (error) {
        console.error('❌ Failed to list documents:', error.message);
        throw error;
    }
}

async function createDocument(pageConfig) {
    try {
        console.log(`📝 Creating document: ${pageConfig.title}...`);

        // Read content from file
        const filePath = path.join(__dirname, pageConfig.file);
        if (!fs.existsSync(filePath)) {
            throw new Error(`Documentation file not found: ${filePath}`);
        }

        const content = fs.readFileSync(filePath, 'utf8');

        // Get or create category
        const categoryId = await getOrCreateCategory(pageConfig.category);

        const response = await makeReadmeRequest(README_ENDPOINTS.docs, {
            method: 'POST',
            body: JSON.stringify({
                title: pageConfig.title,
                slug: pageConfig.slug,
                body: content,
                category: categoryId,
                type: pageConfig.type || 'basic',
                hidden: false
            })
        });

        console.log(`✅ Created document: ${pageConfig.title}`);
        console.log(`🔗 URL: ${PROJECT_INFO.url}/docs/${pageConfig.slug}`);

        return response;

    } catch (error) {
        console.error(`❌ Failed to create document ${pageConfig.title}:`, error.message);
        throw error;
    }
}

async function updateDocument(slug, pageConfig) {
    try {
        console.log(`🔄 Updating document: ${pageConfig.title}...`);

        // Read content from file
        const filePath = path.join(__dirname, pageConfig.file);
        if (!fs.existsSync(filePath)) {
            throw new Error(`Documentation file not found: ${filePath}`);
        }

        const content = fs.readFileSync(filePath, 'utf8');

        const response = await makeReadmeRequest(`${README_ENDPOINTS.docs}/${slug}`, {
            method: 'PUT',
            body: JSON.stringify({
                title: pageConfig.title,
                body: content,
                type: pageConfig.type || 'basic',
                hidden: false
            })
        });

        console.log(`✅ Updated document: ${pageConfig.title}`);
        return response;

    } catch (error) {
        console.error(`❌ Failed to update document ${slug}:`, error.message);
        throw error;
    }
}

async function deleteDocument(slug) {
    try {
        console.log(`🗑️  Deleting document: ${slug}...`);

        const response = await makeReadmeRequest(`${README_ENDPOINTS.docs}/${slug}`, {
            method: 'DELETE'
        });

        console.log(`✅ Deleted document: ${slug}`);
        return response;

    } catch (error) {
        console.error(`❌ Failed to delete document ${slug}:`, error.message);
        throw error;
    }
}

async function getOrCreateCategory(categoryName) {
    try {
        // Get existing categories
        const categories = await makeReadmeRequest(README_ENDPOINTS.categories, {
            method: 'GET'
        });

        // Find existing category
        const existingCategory = categories.find(cat => cat.title === categoryName);
        if (existingCategory) {
            return existingCategory._id;
        }

        // Create new category
        console.log(`📁 Creating category: ${categoryName}...`);
        const newCategory = await makeReadmeRequest(README_ENDPOINTS.categories, {
            method: 'POST',
            body: JSON.stringify({
                title: categoryName,
                type: 'guide'
            })
        });

        console.log(`✅ Created category: ${categoryName}`);
        return newCategory._id;

    } catch (error) {
        console.error(`❌ Failed to get/create category ${categoryName}:`, error.message);
        throw error;
    }
}

async function syncAllDocuments() {
    try {
        console.log('🔄 Syncing all documentation pages...');

        for (const pageConfig of DOC_PAGES) {
            try {
                // Try to update first, create if it doesn't exist
                try {
                    await updateDocument(pageConfig.slug, pageConfig);
                } catch (updateError) {
                    if (updateError.message.includes('404')) {
                        await createDocument(pageConfig);
                    } else {
                        throw updateError;
                    }
                }
            } catch (error) {
                console.error(`⚠️  Failed to sync ${pageConfig.title}:`, error.message);
            }
        }

        console.log('✅ Documentation sync completed!');
        console.log(`📖 Visit your docs: ${PROJECT_INFO.url}`);

    } catch (error) {
        console.error('❌ Failed to sync documents:', error.message);
        throw error;
    }
}

async function getDocument(slug) {
    try {
        console.log(`📄 Fetching document: ${slug}...`);

        const response = await makeReadmeRequest(`${README_ENDPOINTS.docs}/${slug}`, {
            method: 'GET'
        });

        console.log(`✅ Retrieved document: ${response.title}`);
        return response;

    } catch (error) {
        console.error(`❌ Failed to get document ${slug}:`, error.message);
        throw error;
    }
}

// CLI interface
async function main() {
    const command = process.argv[2] || 'sync';
    const slug = process.argv[3];

    try {
        switch (command) {
            case 'list':
                await listDocuments();
                break;

            case 'sync':
                await syncAllDocuments();
                break;

            case 'create':
                if (!slug) {
                    console.error('❌ Please provide a page slug');
                    process.exit(1);
                }
                const pageConfig = DOC_PAGES.find(p => p.slug === slug);
                if (!pageConfig) {
                    console.error(`❌ Page configuration not found for slug: ${slug}`);
                    process.exit(1);
                }
                await createDocument(pageConfig);
                break;

            case 'update':
                if (!slug) {
                    console.error('❌ Please provide a page slug');
                    process.exit(1);
                }
                const updateConfig = DOC_PAGES.find(p => p.slug === slug);
                if (!updateConfig) {
                    console.error(`❌ Page configuration not found for slug: ${slug}`);
                    process.exit(1);
                }
                await updateDocument(slug, updateConfig);
                break;

            case 'delete':
                if (!slug) {
                    console.error('❌ Please provide a page slug');
                    process.exit(1);
                }
                await deleteDocument(slug);
                break;

            case 'get':
                if (!slug) {
                    console.error('❌ Please provide a page slug');
                    process.exit(1);
                }
                const doc = await getDocument(slug);
                console.log(JSON.stringify(doc, null, 2));
                break;

            default:
                console.log(`
Usage: node manage-docs.js [command] [slug]

Commands:
  list            List all documentation pages
  sync            Sync all documentation pages (default)
  create <slug>   Create a specific page
  update <slug>   Update a specific page
  delete <slug>   Delete a specific page
  get <slug>      Get a specific page

Available page slugs:
${DOC_PAGES.map(p => `  - ${p.slug} (${p.title})`).join('\n')}

Examples:
  node manage-docs.js list
  node manage-docs.js sync
  node manage-docs.js create getting-started
  node manage-docs.js update credit-system
                `);
                break;
        }

    } catch (error) {
        console.error('❌ Operation failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export {
    listDocuments,
    createDocument,
    updateDocument,
    deleteDocument,
    syncAllDocuments,
    getDocument,
    DOC_PAGES
};

# Readme.com Documentation Tools

This folder contains a comprehensive set of tools for managing your StalkAPI documentation on Readme.com via their API.

## 🚀 Quick Start

### 1. Complete Deployment
Deploy everything at once (recommended for first-time setup):

```bash
node tools/readme/deploy-all.js
```

This will:
- ✅ Setup default categories
- ✅ Upload OpenAPI specification
- ✅ Sync all documentation pages
- ✅ Provide deployment summary

### 2. Check Status
Check what's currently deployed:

```bash
node tools/readme/deploy-all.js status
```

## 🛠️ Individual Tools

### OpenAPI Management
Manage your API specification:

```bash
# Upload/sync OpenAPI spec
node tools/readme/sync-openapi.js sync

# Update existing spec
node tools/readme/sync-openapi.js update

# Get current spec
node tools/readme/sync-openapi.js get

# Delete spec
node tools/readme/sync-openapi.js delete
```

### Category Management
Manage documentation categories:

```bash
# List all categories
node tools/readme/manage-categories.js list

# Setup default categories
node tools/readme/manage-categories.js setup

# Create a new category
node tools/readme/manage-categories.js create "My Category" guide 1

# Update a category
node tools/readme/manage-categories.js update CATEGORY_ID "New Title" guide 2

# Delete a category
node tools/readme/manage-categories.js delete CATEGORY_ID
```

### Documentation Management
Manage individual documentation pages:

```bash
# List all documents
node tools/readme/manage-docs.js list

# Sync all documents
node tools/readme/manage-docs.js sync

# Create specific document
node tools/readme/manage-docs.js create getting-started

# Update specific document
node tools/readme/manage-docs.js update credit-system

# Delete document
node tools/readme/manage-docs.js delete getting-started
```

## 📁 File Structure

```
tools/readme/
├── README.md              # This file
├── config.js              # Shared configuration
├── deploy-all.js          # Complete deployment script
├── sync-openapi.js        # OpenAPI specification management
├── manage-categories.js   # Category management
└── manage-docs.js         # Documentation page management
```

## ⚙️ Configuration

Configuration is loaded from your `.env` file:

```env
README_API_KEY=***************************************************************************
README_PROJECT_SLUG=stalkapi
README_VERSION=v1.0
```

## 📖 Available Documentation Pages

The tools automatically sync these documentation pages:

| Page | Source File | Category |
|------|-------------|----------|
| Getting Started | `docs/SETUP_GUIDE.md` | Getting Started |
| Credit System | `docs/CREDIT_SYSTEM_GUIDE.md` | Core Concepts |
| KOL Feed Guide | `docs/KOL_FEED_GUIDE.md` | Features |
| KOL Feed History API | `docs/KOL_FEED_HISTORY_API.md` | API Reference |
| Postman Collection | `docs/POSTMAN_GUIDE.md` | Tools & SDKs |

## 📁 Default Categories

The tools create these categories automatically:

1. **Getting Started** (guide) - Basic setup and introduction
2. **Core Concepts** (guide) - Credit system, authentication, etc.
3. **API Reference** (reference) - OpenAPI spec and endpoint docs
4. **Features** (guide) - Feature-specific guides
5. **Tools & SDKs** (guide) - Development tools and SDKs
6. **Advanced** (guide) - Advanced topics and customization

## 🔧 Customization

### Adding New Documentation Pages

Edit `tools/readme/manage-docs.js` and add to the `DOC_PAGES` array:

```javascript
{
    slug: 'my-new-page',
    title: 'My New Page',
    category: 'Getting Started',
    file: '../../docs/MY_NEW_PAGE.md',
    type: 'basic'
}
```

### Adding New Categories

Edit `tools/readme/manage-categories.js` and add to the `DEFAULT_CATEGORIES` array:

```javascript
{
    title: 'My Category',
    type: 'guide',
    order: 7
}
```

### Updating OpenAPI Spec

The OpenAPI specification is automatically generated from `docs/openapi.yaml`. To update:

1. Edit `docs/openapi.yaml`
2. Run `node tools/readme/sync-openapi.js sync`

## 🚨 Error Handling

### Common Issues

**Invalid API Key:**
```
❌ Invalid README_API_KEY format. Should start with "rdme_"
```
- Check your API key in `.env` file
- Get a new key from Readme.com dashboard

**File Not Found:**
```
❌ Documentation file not found: /path/to/file.md
```
- Ensure all documentation files exist
- Check file paths in configuration

**Category Not Found:**
```
❌ Page configuration not found for slug: my-page
```
- Add page configuration to `DOC_PAGES` array
- Check slug spelling

### Debug Mode

Add debug logging by setting environment variable:

```bash
DEBUG=readme:* node tools/readme/deploy-all.js
```

## 🔄 Automation

### GitHub Actions

Create `.github/workflows/docs-deploy.yml`:

```yaml
name: Deploy Documentation

on:
  push:
    branches: [main]
    paths: ['docs/**']

jobs:
  deploy-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm install
        
      - name: Deploy documentation
        env:
          README_API_KEY: ${{ secrets.README_API_KEY }}
          README_PROJECT_SLUG: stalkapi
        run: node tools/readme/deploy-all.js
```

### Scheduled Updates

Update documentation daily:

```yaml
on:
  schedule:
    - cron: '0 6 * * *'  # Daily at 6 AM UTC
```

## 📊 Monitoring

### Deployment Status

Check what's currently deployed:

```bash
node tools/readme/deploy-all.js status
```

### API Usage

Monitor your Readme.com API usage in their dashboard:
- Go to Configuration → API Key
- View usage statistics and rate limits

## 🎯 Best Practices

1. **Test Locally First**: Always validate your OpenAPI spec before deploying
2. **Backup Content**: Export your documentation before major changes
3. **Version Control**: Keep all documentation in version control
4. **Incremental Updates**: Use individual tools for small changes
5. **Monitor Deployments**: Check status after deployments

## 🆘 Support

- **Readme.com Documentation**: [docs.readme.com](https://docs.readme.com)
- **API Reference**: [docs.readme.com/reference](https://docs.readme.com/reference)
- **Community**: [readme.com/community](https://readme.com/community)

## 🎉 Success!

After running the deployment, your documentation will be live at:
- **Main URL**: https://stalkapi.readme.io
- **API Reference**: https://stalkapi.readme.io/reference
- **Custom Domain**: https://docs.stalkapi.com (if configured)

Your professional API documentation is now ready! 🚀

#!/usr/bin/env node

/**
 * Sync OpenAPI Specification with Readme.com
 * Uploads the OpenAPI spec and creates interactive API documentation
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { makeReadmeRequest, README_ENDPOINTS, PROJECT_INFO } from './config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const OPENAPI_FILE = path.join(__dirname, '../../openapi.yaml');

async function syncOpenAPISpec() {
    try {
        console.log('🚀 Syncing OpenAPI specification with Readme.com...');

        // Check if OpenAPI file exists
        if (!fs.existsSync(OPENAPI_FILE)) {
            throw new Error(`OpenAPI file not found: ${OPENAPI_FILE}`);
        }

        // Read OpenAPI specification
        const openApiSpec = fs.readFileSync(OPENAPI_FILE, 'utf8');
        console.log(`📄 Loaded OpenAPI spec: ${(openApiSpec.length / 1024).toFixed(1)}KB`);

        // Upload OpenAPI specification
        const response = await makeReadmeRequest(README_ENDPOINTS.apiSpec, {
            method: 'POST',
            body: JSON.stringify({
                spec: openApiSpec
            })
        });

        console.log('✅ OpenAPI specification uploaded successfully!');
        console.log(`📖 API Reference: ${PROJECT_INFO.url}/reference`);
        
        if (response.id) {
            console.log(`🆔 Specification ID: ${response.id}`);
        }

        return response;

    } catch (error) {
        console.error('❌ Failed to sync OpenAPI specification:', error.message);
        throw error;
    }
}

async function updateAPISpecification(specContent) {
    try {
        console.log('🔄 Updating existing API specification...');

        const response = await makeReadmeRequest(README_ENDPOINTS.apiSpec, {
            method: 'PUT',
            body: JSON.stringify({
                spec: specContent
            })
        });

        console.log('✅ API specification updated successfully!');
        return response;

    } catch (error) {
        console.error('❌ Failed to update API specification:', error.message);
        throw error;
    }
}

async function getAPISpecification() {
    try {
        console.log('📋 Fetching current API specification...');

        const response = await makeReadmeRequest(README_ENDPOINTS.apiSpec, {
            method: 'GET'
        });

        console.log('✅ API specification retrieved successfully!');
        return response;

    } catch (error) {
        console.error('❌ Failed to fetch API specification:', error.message);
        throw error;
    }
}

async function deleteAPISpecification() {
    try {
        console.log('🗑️  Deleting API specification...');

        const response = await makeReadmeRequest(README_ENDPOINTS.apiSpec, {
            method: 'DELETE'
        });

        console.log('✅ API specification deleted successfully!');
        return response;

    } catch (error) {
        console.error('❌ Failed to delete API specification:', error.message);
        throw error;
    }
}

// CLI interface
async function main() {
    const command = process.argv[2] || 'sync';

    try {
        switch (command) {
            case 'sync':
            case 'upload':
                await syncOpenAPISpec();
                break;

            case 'update':
                const specContent = fs.readFileSync(OPENAPI_FILE, 'utf8');
                await updateAPISpecification(specContent);
                break;

            case 'get':
            case 'fetch':
                const spec = await getAPISpecification();
                console.log('Current specification:', JSON.stringify(spec, null, 2));
                break;

            case 'delete':
                await deleteAPISpecification();
                break;

            default:
                console.log(`
Usage: node sync-openapi.js [command]

Commands:
  sync, upload    Upload OpenAPI specification (default)
  update          Update existing specification
  get, fetch      Fetch current specification
  delete          Delete specification

Examples:
  node sync-openapi.js sync
  node sync-openapi.js update
  node sync-openapi.js get
                `);
                break;
        }

    } catch (error) {
        console.error('❌ Operation failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export {
    syncOpenAPISpec,
    updateAPISpecification,
    getAPISpecification,
    deleteAPISpecification
};

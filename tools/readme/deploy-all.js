#!/usr/bin/env node

/**
 * Complete Readme.com Deployment Script
 * Deploys all documentation, API specs, and categories in the correct order
 */

import { validateConfig, PROJECT_INFO } from './config.js';
import { syncOpenAPISpec } from './sync-openapi.js';
import { setupDefaultCategories } from './manage-categories.js';
import { syncAllDocuments } from './manage-docs.js';

async function deployAll() {
    try {
        console.log('🚀 Starting complete Readme.com deployment...\n');

        // Validate configuration
        console.log('1️⃣  Validating configuration...');
        validateConfig();
        console.log('✅ Configuration validated\n');

        // Setup categories first
        console.log('2️⃣  Setting up categories...');
        await setupDefaultCategories();
        console.log('✅ Categories setup completed\n');

        // Sync OpenAPI specification
        console.log('3️⃣  Syncing OpenAPI specification...');
        await syncOpenAPISpec();
        console.log('✅ OpenAPI specification synced\n');

        // Sync all documentation pages
        console.log('4️⃣  Syncing documentation pages...');
        await syncAllDocuments();
        console.log('✅ Documentation pages synced\n');

        // Deployment complete
        console.log('🎉 Deployment completed successfully!');
        console.log('\n📖 Your documentation is now live at:');
        console.log(`   ${PROJECT_INFO.url}`);
        
        if (PROJECT_INFO.customUrl) {
            console.log(`   ${PROJECT_INFO.customUrl} (custom domain)`);
        }

        console.log('\n📋 What was deployed:');
        console.log('   ✅ Default categories structure');
        console.log('   ✅ Interactive OpenAPI documentation');
        console.log('   ✅ Getting Started guide');
        console.log('   ✅ Credit System documentation');
        console.log('   ✅ KOL Feed guides');
        console.log('   ✅ Postman collection guide');

        console.log('\n🎯 Next steps:');
        console.log('   1. Visit your documentation site');
        console.log('   2. Customize branding in Readme.com dashboard');
        console.log('   3. Add team members and set permissions');
        console.log('   4. Configure custom domain (optional)');
        console.log('   5. Set up analytics and feedback collection');

    } catch (error) {
        console.error('❌ Deployment failed:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('   1. Check your README_API_KEY in .env file');
        console.log('   2. Verify your Readme.com project exists');
        console.log('   3. Ensure all documentation files exist');
        console.log('   4. Check your internet connection');
        process.exit(1);
    }
}

async function checkDeploymentStatus() {
    try {
        console.log('🔍 Checking deployment status...\n');

        // Import functions for status checking
        const { listCategories } = await import('./manage-categories.js');
        const { listDocuments } = await import('./manage-docs.js');
        const { getAPISpecification } = await import('./sync-openapi.js');

        // Check categories
        console.log('📁 Categories:');
        const categories = await listCategories();
        
        // Check documents
        console.log('\n📄 Documents:');
        const documents = await listDocuments();
        
        // Check API specification
        console.log('\n📋 API Specification:');
        try {
            const apiSpec = await getAPISpecification();
            console.log('✅ API specification is deployed');
        } catch (error) {
            console.log('❌ API specification not found');
        }

        console.log(`\n📖 Documentation URL: ${PROJECT_INFO.url}`);
        
        return {
            categories: categories.length,
            documents: documents.length,
            hasApiSpec: true
        };

    } catch (error) {
        console.error('❌ Failed to check deployment status:', error.message);
        throw error;
    }
}

async function cleanupDeployment() {
    try {
        console.log('🧹 Cleaning up deployment...\n');
        console.log('⚠️  This will delete ALL documentation from Readme.com!');
        console.log('Are you sure you want to continue? (This action cannot be undone)');
        
        // In a real scenario, you'd want to add confirmation prompt
        // For now, we'll just log what would be deleted
        
        const { listCategories } = await import('./manage-categories.js');
        const { listDocuments } = await import('./manage-docs.js');
        
        const categories = await listCategories();
        const documents = await listDocuments();
        
        console.log(`Would delete ${categories.length} categories and ${documents.length} documents`);
        console.log('Cleanup cancelled for safety. Implement confirmation prompt if needed.');

    } catch (error) {
        console.error('❌ Failed to cleanup deployment:', error.message);
        throw error;
    }
}

// CLI interface
async function main() {
    const command = process.argv[2] || 'deploy';

    try {
        switch (command) {
            case 'deploy':
            case 'all':
                await deployAll();
                break;

            case 'status':
            case 'check':
                await checkDeploymentStatus();
                break;

            case 'cleanup':
            case 'clean':
                await cleanupDeployment();
                break;

            default:
                console.log(`
Usage: node deploy-all.js [command]

Commands:
  deploy, all     Deploy all documentation (default)
  status, check   Check deployment status
  cleanup, clean  Cleanup deployment (removes all docs)

Examples:
  node deploy-all.js deploy
  node deploy-all.js status
  node deploy-all.js cleanup

This script will:
  1. Validate Readme.com configuration
  2. Setup default categories
  3. Upload OpenAPI specification
  4. Sync all documentation pages
  5. Provide deployment summary and next steps
                `);
                break;
        }

    } catch (error) {
        console.error('❌ Operation failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export {
    deployAll,
    checkDeploymentStatus,
    cleanupDeployment
};

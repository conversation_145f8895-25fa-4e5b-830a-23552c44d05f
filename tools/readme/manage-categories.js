#!/usr/bin/env node

/**
 * Manage Categories on Readme.com
 * Create, update, delete, and list documentation categories
 */

import { makeReadmeRequest, README_ENDPOINTS, PROJECT_INFO } from './config.js';

// Default categories structure
const DEFAULT_CATEGORIES = [
    {
        title: 'Getting Started',
        type: 'guide',
        order: 1
    },
    {
        title: 'Core Concepts',
        type: 'guide',
        order: 2
    },
    {
        title: 'API Reference',
        type: 'reference',
        order: 3
    },
    {
        title: 'Features',
        type: 'guide',
        order: 4
    },
    {
        title: 'Tools & SDKs',
        type: 'guide',
        order: 5
    },
    {
        title: 'Advanced',
        type: 'guide',
        order: 6
    }
];

async function listCategories() {
    try {
        console.log('📁 Fetching all categories...');

        const response = await makeReadmeRequest(README_ENDPOINTS.categories, {
            method: 'GET'
        });

        console.log(`✅ Found ${response.length} categories:`);
        response.forEach(category => {
            console.log(`  📁 ${category.title} (${category.type}) - Order: ${category.order || 'N/A'}`);
            console.log(`     ID: ${category._id}`);
        });

        return response;

    } catch (error) {
        console.error('❌ Failed to list categories:', error.message);
        throw error;
    }
}

async function createCategory(categoryConfig) {
    try {
        console.log(`📁 Creating category: ${categoryConfig.title}...`);

        const response = await makeReadmeRequest(README_ENDPOINTS.categories, {
            method: 'POST',
            body: JSON.stringify({
                title: categoryConfig.title,
                type: categoryConfig.type || 'guide',
                order: categoryConfig.order || 999
            })
        });

        console.log(`✅ Created category: ${categoryConfig.title}`);
        console.log(`🆔 Category ID: ${response._id}`);

        return response;

    } catch (error) {
        console.error(`❌ Failed to create category ${categoryConfig.title}:`, error.message);
        throw error;
    }
}

async function updateCategory(categoryId, categoryConfig) {
    try {
        console.log(`🔄 Updating category: ${categoryConfig.title}...`);

        const response = await makeReadmeRequest(`${README_ENDPOINTS.categories}/${categoryId}`, {
            method: 'PUT',
            body: JSON.stringify({
                title: categoryConfig.title,
                type: categoryConfig.type || 'guide',
                order: categoryConfig.order || 999
            })
        });

        console.log(`✅ Updated category: ${categoryConfig.title}`);
        return response;

    } catch (error) {
        console.error(`❌ Failed to update category ${categoryId}:`, error.message);
        throw error;
    }
}

async function deleteCategory(categoryId) {
    try {
        console.log(`🗑️  Deleting category: ${categoryId}...`);

        const response = await makeReadmeRequest(`${README_ENDPOINTS.categories}/${categoryId}`, {
            method: 'DELETE'
        });

        console.log(`✅ Deleted category: ${categoryId}`);
        return response;

    } catch (error) {
        console.error(`❌ Failed to delete category ${categoryId}:`, error.message);
        throw error;
    }
}

async function getCategory(categoryId) {
    try {
        console.log(`📁 Fetching category: ${categoryId}...`);

        const response = await makeReadmeRequest(`${README_ENDPOINTS.categories}/${categoryId}`, {
            method: 'GET'
        });

        console.log(`✅ Retrieved category: ${response.title}`);
        return response;

    } catch (error) {
        console.error(`❌ Failed to get category ${categoryId}:`, error.message);
        throw error;
    }
}

async function setupDefaultCategories() {
    try {
        console.log('🔄 Setting up default categories...');

        // Get existing categories
        const existingCategories = await listCategories();
        
        for (const categoryConfig of DEFAULT_CATEGORIES) {
            const existingCategory = existingCategories.find(cat => cat.title === categoryConfig.title);
            
            if (existingCategory) {
                console.log(`⚠️  Category "${categoryConfig.title}" already exists, skipping...`);
            } else {
                await createCategory(categoryConfig);
            }
        }

        console.log('✅ Default categories setup completed!');
        console.log(`📖 Visit your docs: ${PROJECT_INFO.url}`);

    } catch (error) {
        console.error('❌ Failed to setup default categories:', error.message);
        throw error;
    }
}

async function reorderCategories(categoryOrders) {
    try {
        console.log('🔄 Reordering categories...');

        for (const { id, order } of categoryOrders) {
            await makeReadmeRequest(`${README_ENDPOINTS.categories}/${id}`, {
                method: 'PUT',
                body: JSON.stringify({ order })
            });
            console.log(`✅ Updated order for category ${id}: ${order}`);
        }

        console.log('✅ Categories reordered successfully!');

    } catch (error) {
        console.error('❌ Failed to reorder categories:', error.message);
        throw error;
    }
}

// CLI interface
async function main() {
    const command = process.argv[2] || 'list';
    const categoryId = process.argv[3];

    try {
        switch (command) {
            case 'list':
                await listCategories();
                break;

            case 'setup':
                await setupDefaultCategories();
                break;

            case 'create':
                const title = process.argv[3];
                const type = process.argv[4] || 'guide';
                const order = parseInt(process.argv[5]) || 999;
                
                if (!title) {
                    console.error('❌ Please provide a category title');
                    process.exit(1);
                }
                
                await createCategory({ title, type, order });
                break;

            case 'update':
                if (!categoryId) {
                    console.error('❌ Please provide a category ID');
                    process.exit(1);
                }
                
                const updateTitle = process.argv[4];
                const updateType = process.argv[5] || 'guide';
                const updateOrder = parseInt(process.argv[6]) || 999;
                
                if (!updateTitle) {
                    console.error('❌ Please provide a category title');
                    process.exit(1);
                }
                
                await updateCategory(categoryId, { title: updateTitle, type: updateType, order: updateOrder });
                break;

            case 'delete':
                if (!categoryId) {
                    console.error('❌ Please provide a category ID');
                    process.exit(1);
                }
                await deleteCategory(categoryId);
                break;

            case 'get':
                if (!categoryId) {
                    console.error('❌ Please provide a category ID');
                    process.exit(1);
                }
                const category = await getCategory(categoryId);
                console.log(JSON.stringify(category, null, 2));
                break;

            default:
                console.log(`
Usage: node manage-categories.js [command] [options]

Commands:
  list                                    List all categories
  setup                                   Setup default categories
  create <title> [type] [order]          Create a new category
  update <id> <title> [type] [order]     Update a category
  delete <id>                             Delete a category
  get <id>                                Get a specific category

Category Types:
  - guide (default)
  - reference

Examples:
  node manage-categories.js list
  node manage-categories.js setup
  node manage-categories.js create "My Category" guide 1
  node manage-categories.js update 507f1f77bcf86cd799439011 "Updated Title" guide 2
                `);
                break;
        }

    } catch (error) {
        console.error('❌ Operation failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export {
    listCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategory,
    setupDefaultCategories,
    reorderCategories,
    DEFAULT_CATEGORIES
};

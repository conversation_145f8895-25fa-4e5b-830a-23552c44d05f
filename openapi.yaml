openapi: 3.0.3
info:
  title: StalkAPI
  description: |
    StalkAPI provides real-time KOL (Key Opinion Leader) trading data and analytics through both REST API and WebSocket streaming.
    
    ## Authentication
    StalkAPI supports two authentication methods:
    - **JWT Token**: Obtained via login endpoint
    - **API Key**: Generated for each user account
    
    ## Credit System
    Most endpoints consume credits based on your tier:
    - **Free Tier**: 1,000 credits/month
    - **Basic Tier**: 10,000 credits/month  
    - **Premium Tier**: 100,000 credits/month
    - **Enterprise Tier**: Unlimited credits
    
    ## Rate Limiting
    API endpoints are rate-limited based on your tier to ensure fair usage.
    
    ## WebSocket Streaming
    Real-time data is available through WebSocket connections at `wss://data.stalkapi.com/ws`
    
  version: 1.0.0
  contact:
    name: StalkAPI Support
    url: https://stalkapi.com
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://stalkapi.com/terms

servers:
  - url: https://data.stalkapi.com
    description: Production server
  - url: http://localhost:3000
    description: Development server

security:
  - BearerAuth: []
  - ApiKeyAuth: []

tags:
  - name: System
    description: System health and information endpoints
  - name: Authentication
    description: User authentication and profile management
  - name: KOL Feed
    description: KOL (Key Opinion Leader) trading data and history
  - name: WebSocket API
    description: WebSocket connection management and streaming information

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from login endpoint
    ApiKeyAuth:
      type: apiKey
      in: query
      name: apiKey
      description: API key for authentication

  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
      required:
        - error

    User:
      type: object
      properties:
        id:
          type: integer
          description: User ID
        email:
          type: string
          format: email
          description: User email
        tier_name:
          type: string
          description: User tier (free, basic, premium, enterprise)
        credits_remaining:
          type: integer
          description: Remaining credits
        max_websocket_connections:
          type: integer
          description: Maximum allowed WebSocket connections
        allowed_streams:
          type: array
          items:
            type: string
          description: List of streams user has access to

    KOLFeedItem:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the feed item
        timestamp:
          type: string
          format: date-time
          description: When the trading activity occurred
        kol_name:
          type: string
          description: Name of the KOL
        action:
          type: string
          enum: [buy, sell]
          description: Trading action
        token:
          type: string
          description: Token symbol
        amount:
          type: number
          description: Amount traded
        price:
          type: number
          description: Price at time of trade
        market_cap:
          type: number
          description: Market cap at time of trade

    WebSocketInfo:
      type: object
      properties:
        endpoint:
          type: string
          description: WebSocket endpoint URL
        protocols:
          type: array
          items:
            type: string
          description: Supported protocols
        authentication:
          type: object
          properties:
            methods:
              type: array
              items:
                type: string
              description: Available authentication methods
            jwt_parameter:
              type: string
              description: Parameter name for JWT token
            api_key_parameter:
              type: string
              description: Parameter name for API key
        connection_limits:
          type: object
          properties:
            max_connections:
              type: integer
              description: Maximum connections for user's tier
            current_tier:
              type: string
              description: User's current tier
        available_streams:
          type: array
          items:
            type: string
          description: Streams available to user

paths:
  /:
    get:
      tags:
        - System
      summary: API Root
      description: Get basic API information and WebSocket endpoint
      security: []
      responses:
        '200':
          description: API information
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "StalkApi"
                  version:
                    type: string
                    example: "1.0.0"
                  timestamp:
                    type: string
                    format: date-time
                  websocket:
                    type: object
                    properties:
                      endpoint:
                        type: string
                        example: "wss://data.stalkapi.com/ws"
                      authentication:
                        type: string
                        example: "JWT token or API key required"

  /api/v1/status:
    get:
      tags:
        - System
      summary: System Status
      description: Get system health status (public endpoint)
      security: []
      responses:
        '200':
          description: System status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  timestamp:
                    type: string
                    format: date-time
                  version:
                    type: string
                    example: "1.0.0"
                  uptime:
                    type: number
                    description: Server uptime in seconds
                  environment:
                    type: string
                    example: "production"
        '500':
          description: System error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/v1/kol-feed/history:
    get:
      tags:
        - KOL Feed
      summary: KOL Feed History
      description: Get historical KOL trading data (requires Basic tier or higher, consumes 3 credits)
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      parameters:
        - name: limit
          in: query
          description: Number of items to return (max 100)
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 100
        - name: offset
          in: query
          description: Number of items to skip
          required: false
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: KOL feed history data
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/KOLFeedItem'
                  pagination:
                    type: object
                    properties:
                      limit:
                        type: integer
                      offset:
                        type: integer
                      total:
                        type: integer
                      returned:
                        type: integer
                  credits_consumed:
                    type: integer
                    example: 3
                  message:
                    type: string
                    example: "KOL feed history retrieved successfully"
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Insufficient credits or tier access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/register:
    post:
      tags:
        - Authentication
      summary: User Registration
      description: Register a new user account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                password:
                  type: string
                  minLength: 8
                  description: User password (minimum 8 characters)
                tier_id:
                  type: integer
                  description: Tier ID (optional, defaults to 1 for free tier)
              required:
                - email
                - password
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "User created successfully"
                  user:
                    $ref: '#/components/schemas/User'
                  token:
                    type: string
                    description: JWT token
                  refreshToken:
                    type: string
                    description: Refresh token
                  apiKey:
                    type: string
                    description: API key for authentication
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Registration failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/login:
    post:
      tags:
        - Authentication
      summary: User Login
      description: Authenticate user and get access tokens
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                password:
                  type: string
                  description: User password
              required:
                - email
                - password
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Login successful"
                  user:
                    $ref: '#/components/schemas/User'
                  token:
                    type: string
                    description: JWT token
                  refreshToken:
                    type: string
                    description: Refresh token
        '400':
          description: Missing credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Invalid credentials or account deactivated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Login failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/profile:
    get:
      tags:
        - Authentication
      summary: User Profile
      description: Get current user profile and credit information
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      responses:
        '200':
          description: User profile data
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
                  credits:
                    type: object
                    properties:
                      remaining:
                        type: integer
                        description: Credits remaining
                      total:
                        type: integer
                        description: Total credits for current period
                      reset_date:
                        type: string
                        format: date-time
                        description: When credits reset
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /ws-api/info:
    get:
      tags:
        - WebSocket API
      summary: WebSocket Connection Info
      description: Get WebSocket server information and connection details
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      responses:
        '200':
          description: WebSocket connection information
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  websocket:
                    $ref: '#/components/schemas/WebSocketInfo'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /ws-api/streams:
    get:
      tags:
        - WebSocket API
      summary: Available Streams
      description: Get list of streams available to the authenticated user
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      responses:
        '200':
          description: Available streams
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  streams:
                    type: array
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                          description: Stream name
                        description:
                          type: string
                          description: Stream description
                        tier_required:
                          type: string
                          description: Minimum tier required
                        credit_cost:
                          type: integer
                          description: Credits consumed per connection
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /ws-api/sessions:
    get:
      tags:
        - WebSocket API
      summary: Active Sessions
      description: Get information about user's active WebSocket sessions
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      responses:
        '200':
          description: Active sessions information
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  sessions:
                    type: array
                    items:
                      type: object
                      properties:
                        session_id:
                          type: string
                          description: Session identifier
                        connection_id:
                          type: string
                          description: Connection identifier
                        connected_at:
                          type: string
                          format: date-time
                          description: Connection timestamp
                        subscriptions:
                          type: array
                          items:
                            type: string
                          description: Active stream subscriptions
                        ip_address:
                          type: string
                          description: Client IP address
                  total_connections:
                    type: integer
                    description: Total active connections
                  max_connections:
                    type: integer
                    description: Maximum allowed connections
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /ws-api/stats:
    get:
      tags:
        - WebSocket API
      summary: Usage Statistics
      description: Get WebSocket usage statistics for the authenticated user
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      responses:
        '200':
          description: Usage statistics
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  stats:
                    type: object
                    properties:
                      total_connections:
                        type: integer
                        description: Total connections made
                      total_messages:
                        type: integer
                        description: Total messages received
                      credits_consumed:
                        type: integer
                        description: Total credits consumed via WebSocket
                      active_streams:
                        type: array
                        items:
                          type: string
                        description: Currently active streams
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /ws-api/credits:
    get:
      tags:
        - WebSocket API
      summary: Stream Credit Information
      description: Get credit costs and information for WebSocket streams
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      parameters:
        - name: stream
          in: query
          description: Specific stream to get credit information for
          required: false
          schema:
            type: string
            example: "kol-feed"
      responses:
        '200':
          description: Stream credit information
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  credits:
                    type: object
                    properties:
                      stream_costs:
                        type: object
                        description: Credit costs per stream
                        additionalProperties:
                          type: integer
                      user_credits:
                        type: integer
                        description: User's remaining credits
                      billing_period:
                        type: string
                        description: Current billing period
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /ws-api/test:
    post:
      tags:
        - WebSocket API
      summary: Test WebSocket Connection
      description: Test WebSocket connection and get sample connection information
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      responses:
        '200':
          description: WebSocket test information
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "WebSocket test endpoint"
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      email:
                        type: string
                      tier:
                        type: string
                  connection_info:
                    type: object
                    properties:
                      max_connections:
                        type: integer
                      allowed_streams:
                        type: array
                        items:
                          type: string
                  test_urls:
                    type: object
                    properties:
                      with_jwt:
                        type: string
                        example: "ws://localhost:3000/ws?token=YOUR_JWT_TOKEN"
                      with_api_key:
                        type: string
                        example: "ws://localhost:3000/ws?apiKey=YOUR_API_KEY"
                  sample_messages:
                    type: object
                    properties:
                      subscribe:
                        type: string
                        example: '{"type":"subscribe","payload":{"stream":"demo-stream"}}'
                      unsubscribe:
                        type: string
                        example: '{"type":"unsubscribe","payload":{"stream":"demo-stream"}}'
                      ping:
                        type: string
                        example: '{"type":"ping"}'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

# WebSocket Connection Documentation
# Note: OpenAPI 3.0 doesn't natively support WebSocket documentation
# This is documented as a comment for reference

# WebSocket Endpoint: wss://data.stalkapi.com/ws
#
# Authentication:
# - JWT Token: wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN
# - API Key: wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY
#
# Message Types:
# 1. Subscribe to stream:
#    {"type": "subscribe", "payload": {"stream": "kol-feed"}}
#
# 2. Unsubscribe from stream:
#    {"type": "unsubscribe", "payload": {"stream": "kol-feed"}}
#
# 3. Ping (heartbeat):
#    {"type": "ping"}
#
# 4. Pong response:
#    {"type": "pong", "timestamp": "2024-01-01T00:00:00.000Z"}
#
# Stream Data Format:
# {
#   "type": "stream_data",
#   "stream": "kol-feed",
#   "data": {
#     "id": "unique_id",
#     "timestamp": "2024-01-01T00:00:00.000Z",
#     "kol_name": "Example KOL",
#     "action": "buy",
#     "token": "TOKEN",
#     "amount": 1000,
#     "price": 0.001,
#     "market_cap": 1000000
#   }
# }
#
# Available Streams:
# - kol-feed: Real-time KOL trading activity
# - demo-stream: Demo data for testing (Free tier)
#
# Connection Limits:
# - Free Tier: 1 connection
# - Basic Tier: 3 connections
# - Premium Tier: 10 connections
# - Enterprise Tier: 50 connections

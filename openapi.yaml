openapi: 3.0.3
info:
  title: StalkAPI
  description: |
    StalkAPI provides real-time KOL (Key Opinion Leader) trading data and analytics through both REST API and WebSocket streaming.
    
    ## Authentication
    StalkAPI supports two authentication methods:
    - **JWT Token**: Obtained via login endpoint
    - **API Key**: Generated for each user account
    
    ## Credit System
    Most endpoints consume credits based on your tier:
    - **Free Tier**: 1,000 credits/month
    - **Basic Tier**: 10,000 credits/month  
    - **Premium Tier**: 100,000 credits/month
    - **Enterprise Tier**: Unlimited credits
    
    ## Rate Limiting
    API endpoints are rate-limited based on your tier to ensure fair usage.
    
    ## WebSocket Streaming
    Real-time data is available through WebSocket connections at `wss://data.stalkapi.com/ws`
    
  version: 1.0.0
  contact:
    name: StalkAPI Support
    url: https://stalkapi.com
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://stalkapi.com/terms

servers:
  - url: https://data.stalkapi.com
    description: Production server
  - url: http://localhost:3000
    description: Development server

security:
  - BearerAuth: []
  - ApiKeyAuth: []

tags:
  - name: core
    description: Core system and authentication endpoints
  - name: feeds
    description: KOL trading data and historical information
  - name: streaming
    description: WebSocket connection management and streaming



components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from login endpoint
    ApiKeyAuth:
      type: apiKey
      in: query
      name: apiKey
      description: API key for authentication

  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
      required:
        - error

    User:
      type: object
      properties:
        id:
          type: integer
          description: User ID
        email:
          type: string
          format: email
          description: User email
        tier_name:
          type: string
          description: User tier (free, basic, premium, enterprise)
        credits_remaining:
          type: integer
          description: Remaining credits
        max_websocket_connections:
          type: integer
          description: Maximum allowed WebSocket connections
        allowed_streams:
          type: array
          items:
            type: string
          description: List of streams user has access to

    KOLFeedItem:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the feed item
        timestamp:
          type: string
          format: date-time
          description: When the trading activity occurred
        kol_name:
          type: string
          description: Name of the KOL
        action:
          type: string
          enum: [buy, sell]
          description: Trading action
        token:
          type: string
          description: Token symbol
        amount:
          type: number
          description: Amount traded
        price:
          type: number
          description: Price at time of trade
        market_cap:
          type: number
          description: Market cap at time of trade

    WebSocketInfo:
      type: object
      properties:
        endpoint:
          type: string
          description: WebSocket endpoint URL
          example: "wss://data.stalkapi.com/ws"
        protocols:
          type: array
          items:
            type: string
          description: Supported protocols
          example: ["websocket"]
        authentication:
          type: object
          properties:
            methods:
              type: array
              items:
                type: string
              description: Available authentication methods
              example: ["jwt_token", "api_key"]
            jwt_parameter:
              type: string
              description: Parameter name for JWT token
              example: "token"
            api_key_parameter:
              type: string
              description: Parameter name for API key
              example: "apiKey"
        connection_limits:
          type: object
          properties:
            max_connections:
              type: integer
              description: Maximum connections for user's tier
              example: 5
            current_tier:
              type: string
              description: User's current tier
              example: "basic"
        available_streams:
          type: array
          items:
            type: string
          description: Streams available to user
          example: ["kol-feed"]
        message_types:
          type: object
          description: Available WebSocket message types
          properties:
            subscribe:
              type: object
              properties:
                type:
                  type: string
                  example: "subscribe"
                payload:
                  type: object
                  properties:
                    stream:
                      type: string
                      example: "stream_name"
            unsubscribe:
              type: object
              properties:
                type:
                  type: string
                  example: "unsubscribe"
                payload:
                  type: object
                  properties:
                    stream:
                      type: string
                      example: "stream_name"
            ping:
              type: object
              properties:
                type:
                  type: string
                  example: "ping"
        example_connection:
          type: object
          description: Example connection URLs
          properties:
            url:
              type: string
              description: Primary connection URL with JWT
              example: "wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN"
            alternative_url:
              type: string
              description: Alternative connection URL with API key
              example: "wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY"

paths:
  /:
    get:
      tags:
        - core
      summary: API Root
      description: Get basic API information and WebSocket endpoint
      security: []
      responses:
        '200':
          description: API information
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "StalkApi"
                  version:
                    type: string
                    example: "1.0.0"
                  timestamp:
                    type: string
                    format: date-time
                  websocket:
                    type: object
                    properties:
                      endpoint:
                        type: string
                        example: "wss://data.stalkapi.com/ws"
                      authentication:
                        type: string
                        example: "JWT token or API key required"
              example:
                message: "StalkApi"
                version: "1.0.0"
                timestamp: "2025-06-03T07:02:19.649Z"
                websocket:
                  endpoint: "wss://data.stalkapi.com/ws"
                  authentication: "JWT token or API key required"

  /api/v1/status:
    get:
      tags:
        - core
      summary: System Status
      description: Get system health status (public endpoint)
      security: []
      responses:
        '200':
          description: System status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  timestamp:
                    type: string
                    format: date-time
                  version:
                    type: string
                    example: "1.0.0"
                  uptime:
                    type: number
                    description: Server uptime in seconds
                  services:
                    type: object
                    properties:
                      database:
                        type: string
                        example: "healthy"
                      redis:
                        type: string
                        example: "healthy"
                  memory:
                    type: object
                    properties:
                      rss:
                        type: integer
                      heapTotal:
                        type: integer
                      heapUsed:
                        type: integer
                      external:
                        type: integer
                      arrayBuffers:
                        type: integer
              example:
                status: "healthy"
                timestamp: "2025-06-03T07:02:19.643Z"
                services:
                  database: "healthy"
                  redis: "healthy"
                uptime: 1224.419303916
                memory:
                  rss: 65159168
                  heapTotal: 20660224
                  heapUsed: 18038048
                  external: 3850985
                  arrayBuffers: 230519
                version: "1.0.0"
        '500':
          description: System error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/v1/kol-feed/history:
    get:
      tags:
        - feeds
      summary: KOL Feed History
      description: Get historical KOL trading data (requires Basic tier or higher, consumes 3 credits)
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      parameters:
        - name: limit
          in: query
          description: Number of items to return (max 100)
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 100
        - name: offset
          in: query
          description: Number of items to skip
          required: false
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: KOL feed history data
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/KOLFeedItem'
                  pagination:
                    type: object
                    properties:
                      limit:
                        type: integer
                      offset:
                        type: integer
                      total:
                        type: integer
                      returned:
                        type: integer
                  credits_consumed:
                    type: integer
                    example: 3
                  message:
                    type: string
                    example: "KOL feed history retrieved successfully"
              example:
                success: true
                data:
                  - timestamp: 1748934086028
                    kol_label: "YOUNIZ"
                    wallet: "EKDDjxzJ39Bjkr47NiARGJDKFVxiiV9WNJ5XbtEhPEXP"
                    kol_avatar: null
                    tokenIn:
                      symbol: "Gooney"
                      name: "Gooney the Pooh"
                      logo: ""
                      tokenAmountString: "15.76M"
                      amount: 15757476.686031
                      tokenInAmountUsd: 216.1115
                      price: 0.000014
                      mint: "6ogBWYepyDHfx2XuEqres6oSb9nfbNQPgeobPMutpump"
                    tokenOut:
                      symbol: "SOL"
                      name: "Wrapped SOL"
                      logo: "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11***************************************/logo.png"
                      tokenAmountString: "1.329"
                      amount: 1.3290743899999988
                      tokenOutAmountUsd: 212.224816
                      price: 159.678659
                      mint: "So11***************************************"
                    signature: "5KQUFwwhmi25k2bQzjbn6JDiQKghEgvL4hcnLCfBbiqxKzj3j2MDgkMgMGrQUcw1Ni3gAzaEDr8uwLMUfrLt5bZA"
                    transactionType: "sell"
                    chain: "solana"
                    socials:
                      - type: "x"
                        handle: "YOUNIZ_XLZ"
                        followers: 0
                        editedTimestamp: 1746804152219
                pagination:
                  limit: 3
                  offset: 0
                  total: 100
                  returned: 3
                credits_consumed: 3
                message: "KOL feed history retrieved successfully"
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Insufficient credits or tier access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/register:
    post:
      tags:
        - core
      summary: User Registration
      description: Register a new user account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                password:
                  type: string
                  minLength: 8
                  description: User password (minimum 8 characters)
                tier_id:
                  type: integer
                  description: Tier ID (optional, defaults to 1 for free tier)
              required:
                - email
                - password
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "User created successfully"
                  user:
                    $ref: '#/components/schemas/User'
                  token:
                    type: string
                    description: JWT token
                  refreshToken:
                    type: string
                    description: Refresh token
                  apiKey:
                    type: string
                    description: API key for authentication
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Registration failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/login:
    post:
      tags:
        - core
      summary: User Login
      description: Authenticate user and get access tokens
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                password:
                  type: string
                  description: User password
              required:
                - email
                - password
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Login successful"
                  user:
                    $ref: '#/components/schemas/User'
                  token:
                    type: string
                    description: JWT token
                  refreshToken:
                    type: string
                    description: Refresh token
              example:
                message: "Login successful"
                user:
                  id: "ceb60036-51d4-47fe-8aae-a450c18d316f"
                  email: "<EMAIL>"
                  api_key: "2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG"
                  tier_id: 2
                  credits_remaining: 9058
                  tier_name: "basic"
                  max_credits_per_month: 10000
                  allowed_endpoints:
                    - "/api/v1/status"
                    - "/api/v1/kol-feed/history"
                  allowed_streams:
                    - "kol-feed"
                token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.__JId9MiSLkyMlYOHCAaFpyk0FV2gMBkHl3z47vz7dw"
                refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.MbvZl4FzVNxfZnFLBnMz4q9npSZCSYwkwwVUd2KpMHA"
        '400':
          description: Missing credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Invalid credentials or account deactivated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Login failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/profile:
    get:
      tags:
        - Core API
      summary: User Profile
      description: Get current user profile and credit information
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      responses:
        '200':
          description: User profile data
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
                  credits:
                    type: object
                    properties:
                      remaining:
                        type: integer
                        description: Credits remaining
                      total:
                        type: integer
                        description: Total credits for current period
                      reset_date:
                        type: string
                        format: date-time
                        description: When credits reset
              example:
                user:
                  id: "ceb60036-51d4-47fe-8aae-a450c18d316f"
                  email: "<EMAIL>"
                  api_key: "2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG"
                  tier_id: 2
                  credits_remaining: 9058
                  tier_name: "basic"
                  max_credits_per_month: 10000
                  max_requests_per_minute: 60
                  max_websocket_connections: 3
                  allowed_endpoints:
                    - "/api/v1/status"
                    - "/api/v1/kol-feed/history"
                  allowed_streams:
                    - "kol-feed"
                credits:
                  credits_remaining: 9058
                  credits_used_this_month: 945
                  total_credits_purchased: 0
                  max_credits_per_month: 10000
                  tier_name: "basic"
                  unlimited: false
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /ws-api/info:
    get:
      tags:
        - Real-time Streaming
      summary: WebSocket Connection Info
      description: "Get WebSocket server information and connection details.\n\n## 🔌 WebSocket Connection Guide\n\n### 1. Connect to WebSocket\n```javascript\n// Using JWT Token\nconst ws = new WebSocket('wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN');\n\n// Using API Key\nconst ws = new WebSocket('wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY');\n```\n\n### 2. Subscribe to KOL Feed\n```javascript\nws.onopen = () => {\n    console.log('Connected to StalkAPI WebSocket');\n\n    // Subscribe to real-time KOL trading data\n    ws.send(JSON.stringify({\n        type: 'subscribe',\n        payload: { stream: 'kol-feed' }\n    }));\n};\n```\n\n### 3. Handle Real-time Data\n```javascript\nws.onmessage = (event) => {\n    const message = JSON.parse(event.data);\n    \n    if (message.type === 'stream_data' && message.stream === 'kol-feed') {\n        console.log('KOL Trade:', message.data);\n        console.log('KOL:', message.data.kol_label);\n        console.log('Transaction:', message.data.transactionType);\n        console.log('Token In:', message.data.tokenIn.symbol);\n        console.log('Token Out:', message.data.tokenOut.symbol);\n        console.log('USD Value:', message.data.tokenInAmountUsd);\n        // Process real-time trading data\n    }\n};\n```\n\n### 4. Message Types\n\n#### 📤 Client → Server Messages:\n\n**Subscribe to Stream:**\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"kol-feed\"\n  }\n}\n```\n\n**Unsubscribe from Stream:**\n```json\n{\n  \"type\": \"unsubscribe\",\n  \"payload\": {\n    \"stream\": \"kol-feed\"\n  }\n}\n```\n\n**Ping (Heartbeat):**\n```json\n{\n  \"type\": \"ping\"\n}\n```\n\n#### 📥 Server → Client Messages:\n\n**Connection Established:**\n```json\n{\n  \"type\": \"connected\",\n  \"connectionId\": \"uuid-string\",\n  \"message\": \"WebSocket connection established\"\n}\n```\n\n**Subscription Confirmed:**\n```json\n{\n  \"type\": \"subscribed\",\n  \"stream\": \"kol-feed\",\n  \"message\": \"Successfully subscribed to kol-feed\"\n}\n```\n\n**Stream Data (KOL Trade):**\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"kol-feed\",\n  \"data\": {\n    \"timestamp\": 1748933531569,\n    \"kol_label\": \"KOL Name\",\n    \"wallet\": \"wallet_address\",\n    \"transactionType\": \"buy\",\n    \"tokenIn\": {...},\n    \"tokenOut\": {...}\n  }\n}\n```\n\n**Error Message:**\n```json\n{\n  \"type\": \"error\",\n  \"error\": \"Authentication failed\"\n}\n```\n\n**Credit Warning:**\n```json\n{\n  \"type\": \"credit_warning\",\n  \"message\": \"Insufficient credits\",\n  \"credits_remaining\": 0\n}\n```\n\n**Pong Response:**\n```json\n{\n  \"type\": \"pong\",\n  \"timestamp\": \"2024-01-31T12:00:00.000Z\"\n}\n```\n\n### 5. Example KOL Feed Data\n```json\n{\n  \"type\": \"stream_data\",\n  \"stream\": \"kol-feed\",\n  \"data\": {\n    \"timestamp\": 1748933531569,\n    \"kol_label\": \"Mr. Frog, (Road to Redemption Arc)\",\n    \"wallet\": \"4DdrfiDHpmx55i4SPssxVzS9ZaKLb8qr45NKY9Er9nNh\",\n    \"kol_avatar\": \"https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/themisterfrog.jpg\",\n    \"tokenIn\": {\n      \"symbol\": \"SOL\",\n      \"name\": \"Wrapped SOL\",\n      \"logo\": \"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11***************************************/logo.png\",\n      \"tokenAmountString\": \"4\",\n      \"amount\": 3.999992997999996,\n      \"tokenInAmountUsd\": 637.718608,\n      \"price\": 159.429931,\n      \"mint\": \"So11***************************************\"\n    },\n    \"tokenOut\": {\n      \"symbol\": \"KIRICOIN\",\n      \"name\": \"Kiri\",\n      \"logo\": \"https://ipfs.io/ipfs/QmPmxLq9uShXihszcEWBhw865XXEdNPTGaofzhSZ27K79d\",\n      \"tokenAmountString\": \"55.23M\",\n      \"amount\": 55231127.064774,\n      \"tokenOutAmountUsd\": 637.718608,\n      \"price\": 0.000012,\n      \"mint\": \"MrTwBDuwMHbevZYceWhqEWJbCeLj6FkFiqfATWMpump\"\n    },\n    \"signature\": \"2sWhnenTFYYcEc49457LPQoNgFFAMqrcH6Kr3z6y72WmUJ8mFGmMpDfanwe152ipPhabzVECXFjaa52wAqzhFNB8\",\n    \"transactionType\": \"buy\",\n    \"chain\": \"solana\",\n    \"socials\": [\n      {\n        \"type\": \"x\",\n        \"handle\": \"TheMisterFrog\",\n        \"followers\": 124700,\n        \"editedTimestamp\": 1745422721662\n      }\n    ]\n  },\n  \"timestamp\": 1748933531664\n}\n```\n\n### 6. Credit System\n- **KOL Feed Stream**: 2 credits per message\n- **Minimum Tier**: Basic tier or higher\n- **Rate Limiting**: Based on your tier limits"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      responses:
        '200':
          description: WebSocket connection information
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  websocket:
                    $ref: '#/components/schemas/WebSocketInfo'
              example:
                success: true
                websocket:
                  endpoint: "wss://data.stalkapi.com/ws"
                  protocols:
                    - "websocket"
                  authentication:
                    methods:
                      - "jwt_token"
                      - "api_key"
                    jwt_parameter: "token"
                    api_key_parameter: "apiKey"
                  connection_limits:
                    max_connections: 3
                    current_tier: "basic"
                  available_streams:
                    - "kol-feed"
                  message_types:
                    subscribe:
                      type: "subscribe"
                      payload:
                        stream: "stream_name"
                    unsubscribe:
                      type: "unsubscribe"
                      payload:
                        stream: "stream_name"
                    ping:
                      type: "ping"
                  example_connection:
                    url: "wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN"
                    alternative_url: "wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY"
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /ws-api/streams:
    get:
      tags:
        - Real-time Streaming
      summary: Available Streams
      description: Get list of streams available to the authenticated user
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      responses:
        '200':
          description: Available streams
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  streams:
                    type: array
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                          description: Stream name
                        description:
                          type: string
                          description: Stream description
                        tier_required:
                          type: string
                          description: Minimum tier required
                        credit_cost:
                          type: integer
                          description: Credits consumed per connection
              example:
                success: true
                streams:
                  - name: "kol-feed"
                    description: "Real-time KOL trading activity stream"
                    required_tier: "basic"
                    credits_per_message: 2
                    max_subscribers: 1000
                    metadata:
                      type: "event-driven"
                      format: "json"
                      source: "kol-feed"
                    has_access: true
                user_tier: "basic"
                total_available: 1
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /ws-api/sessions:
    get:
      tags:
        - Real-time Streaming
      summary: Active Sessions
      description: Get information about user's active WebSocket sessions
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      responses:
        '200':
          description: Active sessions information
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  sessions:
                    type: array
                    items:
                      type: object
                      properties:
                        session_id:
                          type: string
                          description: Session identifier
                        connection_id:
                          type: string
                          description: Connection identifier
                        connected_at:
                          type: string
                          format: date-time
                          description: Connection timestamp
                        subscriptions:
                          type: array
                          items:
                            type: string
                          description: Active stream subscriptions
                        ip_address:
                          type: string
                          description: Client IP address
                  total_connections:
                    type: integer
                    description: Total active connections
                  max_connections:
                    type: integer
                    description: Maximum allowed connections
              example:
                success: true
                active_sessions:
                  - session_id: "7239aace-d0b6-415b-a081-aed917cd5762"
                    connection_id: "610aacbb-6628-48e3-8ef9-43687b77670f"
                    subscribed_streams:
                      - "kol-feed"
                    connected_at: "2025-06-03T06:41:59.367Z"
                    last_activity: "2025-06-03T06:42:00.550Z"
                    ip_address: "::1"
                    duration_ms: 1222398
                total_sessions: 1
                max_allowed: 3
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'







# WebSocket Connection Documentation
# Note: OpenAPI 3.0 doesn't natively support WebSocket documentation
# This is documented as a comment for reference

# WebSocket Endpoint: wss://data.stalkapi.com/ws
#
# Authentication:
# - JWT Token: wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN
# - API Key: wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY
#
# Message Types:
# 1. Subscribe to stream:
#    {"type": "subscribe", "payload": {"stream": "kol-feed"}}
#
# 2. Unsubscribe from stream:
#    {"type": "unsubscribe", "payload": {"stream": "kol-feed"}}
#
# 3. Ping (heartbeat):
#    {"type": "ping"}
#
# 4. Pong response:
#    {"type": "pong", "timestamp": "2024-01-01T00:00:00.000Z"}
#
# Stream Data Format:
# {
#   "type": "txSwap",
#   "source": "kol",
#   "tx": {
#     "tokenIn": {
#       "symbol": "RestHere",
#       "name": "stop trenching rest here",
#       "logo": "",
#       "tokenAmountString": "36.63M",
#       "amount": 36634865.514456,
#       "tokenInAmountUsd": 869.859019,
#       "price": 0.000024,
#       "tokenAdress": "HWMU34Zvd11uH19K3dXeTYVywcDide8WLfYy6qaDpump",
#       "tokenAddress": "HWMU34Zvd11uH19K3dXeTYVywcDide8WLfYy6qaDpump"
#     },
#     "tokenOut": {
#       "symbol": "SOL",
#       "name": "Wrapped SOL",
#       "logo": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11***************************************/logo.png",
#       "tokenAmountString": "4.435",
#       "amount": 4.434921151999999,
#       "tokenOutAmountUsd": 707.081907,
#       "price": 159.435057,
#       "tokenAdress": "So11***************************************",
#       "tokenAddress": "So11***************************************"
#     },
#     "timestamp": 1748931162,
#     "chain": "solana",
#     "tx": "3AvKYmn4MBjoqpSKgKZTS3rjHEt1w1CGkA9qXUse4SnMTDPuSePu4RhrqrxXWFrPayw9FtcCco7BDGWMrawr4EYc",
#     "walletAddress": "EKDDjxzJ39Bjkr47NiARGJDKFVxiiV9WNJ5XbtEhPEXP",
#     "wallet_label": "YOUNIZ",
#     "wallet_avatar": null,
#     "isPublic": true,
#     "isKol": true,
#     "socials": [
#       {
#         "type": "x",
#         "handle": "YOUNIZ_XLZ",
#         "followers": 0,
#         "editedTimestamp": 1746804152219
#       }
#     ],
#     "totalUsd": 870,
#     "totalUsdNumber": 869.859019,
#     "transactionType": "sell"
#   },
#   "timestamp": 1748932974241
# }
#
# Available Streams:
# - kol-feed: Real-time KOL trading activity
# - demo-stream: Demo data for testing (Free tier)
#
# Connection Limits:
# - Free Tier: 1 connection
# - Basic Tier: 3 connections
# - Premium Tier: 10 connections
# - Enterprise Tier: 50 connections

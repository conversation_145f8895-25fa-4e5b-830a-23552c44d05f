#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to sync OpenAPI documentation with Readme.com
 * 
 * Usage:
 * 1. Set your README_API_KEY environment variable
 * 2. Run: node scripts/sync-readme.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const README_API_KEY = process.env.README_API_KEY;
const README_PROJECT_SLUG = process.env.README_PROJECT_SLUG || 'stalkapi'; // Your readme.com subdomain
const OPENAPI_FILE = path.join(__dirname, '../docs/openapi.yaml');

if (!README_API_KEY) {
    console.error('❌ README_API_KEY environment variable is required');
    console.log('Get your API key from: https://dash.readme.com/project/YOUR_PROJECT/v1.0/api-key');
    process.exit(1);
}

async function syncOpenAPISpec() {
    try {
        console.log('🚀 Syncing OpenAPI specification with Readme.com...');

        // Read OpenAPI file
        const openApiSpec = fs.readFileSync(OPENAPI_FILE, 'utf8');
        
        // Upload to Readme.com
        const response = await fetch(`https://dash.readme.com/api/v1/api-specification`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${Buffer.from(README_API_KEY + ':').toString('base64')}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                spec: openApiSpec
            })
        });

        if (response.ok) {
            const result = await response.json();
            console.log('✅ OpenAPI specification uploaded successfully!');
            console.log(`📖 View your docs at: https://${README_PROJECT_SLUG}.readme.io`);
            return result;
        } else {
            const error = await response.text();
            console.error('❌ Failed to upload OpenAPI specification:', error);
            throw new Error(`HTTP ${response.status}: ${error}`);
        }
    } catch (error) {
        console.error('❌ Error syncing with Readme.com:', error.message);
        process.exit(1);
    }
}

async function createCustomPages() {
    try {
        console.log('📝 Creating custom documentation pages...');

        // Create Getting Started page
        const gettingStartedContent = `
# Getting Started with StalkAPI

Welcome to StalkAPI! This guide will help you get started with our credit-based API platform.

## Quick Start

1. **Sign up** for an account (contact support)
2. **Get your API key** from your dashboard
3. **Make your first request** using the demo endpoint
4. **Monitor your credits** via the profile endpoint

## Authentication

StalkAPI supports two authentication methods:

### JWT Token Authentication
\`\`\`bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  "https://data.stalkapi.com/api/v1/demo"
\`\`\`

### API Key Authentication
\`\`\`bash
curl -H "X-API-Key: YOUR_API_KEY" \\
  "https://data.stalkapi.com/api/v1/demo"
\`\`\`

## Credit System

All API endpoints consume credits based on complexity:

- **Demo endpoint**: 1 credit
- **Data endpoint**: 2 credits  
- **Analytics**: 5 credits
- **KOL Feed History**: 3 credits

Check your credit balance:
\`\`\`bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  "https://data.stalkapi.com/auth/profile"
\`\`\`

## WebSocket Streaming

Connect to real-time data streams:

\`\`\`javascript
const ws = new WebSocket('wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN');

ws.onopen = () => {
    // Subscribe to KOL feed
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: { stream: 'kol-feed' }
    }));
};
\`\`\`

## Next Steps

- Explore the API reference
- Check out our [Postman collection](https://github.com/your-repo/postman)
- Join our developer community
        `;

        await createReadmePage('getting-started', 'Getting Started', gettingStartedContent);

        // Create Credit System page
        const creditSystemContent = `
# Credit System

StalkAPI uses a credit-based usage tracking system for fair resource allocation.

## How Credits Work

- **Pre-consumption**: Credits are checked before processing
- **Atomic operations**: Database functions ensure consistency  
- **Real-time tracking**: Immediate balance updates
- **Monthly reset**: Credits refresh on the 1st of each month

## Access Tiers

| Tier | Credits/Month | Price | Features |
|------|---------------|-------|----------|
| **Basic** | 10,000 | $9.99 | Standard endpoints, 3 WebSockets |
| **Premium** | 100,000 | $49.99 | Advanced features, 10 WebSockets |
| **Enterprise** | Unlimited | $199.99 | All features, 50 WebSockets |

## Credit Costs

### REST API Endpoints
- \`/api/v1/demo\` - 1 credit
- \`/api/v1/data\` - 2 credits
- \`/api/v1/analytics\` - 5 credits
- \`/api/v1/kol-feed/history\` - 3 credits

### WebSocket Streams
- Connection: 0 credits
- Messages: 1-5 credits per message (varies by stream)

## Monitoring Usage

Check your credit status anytime:

\`\`\`bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  "https://data.stalkapi.com/auth/profile"
\`\`\`

Response includes:
- \`credits_remaining\` - Available credits
- \`credits_used_this_month\` - Monthly usage
- \`max_credits_per_month\` - Tier limit
        `;

        await createReadmePage('credit-system', 'Credit System', creditSystemContent);

        console.log('✅ Custom pages created successfully!');

    } catch (error) {
        console.error('❌ Error creating custom pages:', error.message);
    }
}

async function createReadmePage(slug, title, content) {
    const response = await fetch(`https://dash.readme.com/api/v1/docs`, {
        method: 'POST',
        headers: {
            'Authorization': `Basic ${Buffer.from(README_API_KEY + ':').toString('base64')}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            title: title,
            slug: slug,
            body: content,
            category: '6507f1b1c0c5e90070b1234a', // You'll need to get your category ID
            type: 'basic'
        })
    });

    if (!response.ok) {
        const error = await response.text();
        console.warn(`⚠️  Could not create page "${title}": ${error}`);
    } else {
        console.log(`✅ Created page: ${title}`);
    }
}

// Main execution
async function main() {
    console.log('🔄 Starting Readme.com sync...\n');
    
    await syncOpenAPISpec();
    await createCustomPages();
    
    console.log('\n🎉 Sync completed!');
    console.log(`📖 Visit your documentation: https://${README_PROJECT_SLUG}.readme.io`);
}

main().catch(console.error);

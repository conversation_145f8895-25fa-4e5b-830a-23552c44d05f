#!/usr/bin/env node

/**
 * Simple OpenAPI validation script
 * Validates the OpenAPI specification for basic structure and required fields
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const OPENAPI_FILE = path.join(__dirname, '../docs/openapi.yaml');

function validateOpenAPISpec() {
    try {
        console.log('🔍 Validating OpenAPI specification...');

        // Check if file exists
        if (!fs.existsSync(OPENAPI_FILE)) {
            throw new Error('OpenAPI file not found: ' + OPENAPI_FILE);
        }

        // Read and parse YAML (basic validation)
        const content = fs.readFileSync(OPENAPI_FILE, 'utf8');
        
        // Basic structure checks
        const requiredFields = [
            'openapi:',
            'info:',
            'paths:',
            'components:'
        ];

        const missingFields = requiredFields.filter(field => !content.includes(field));
        
        if (missingFields.length > 0) {
            throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Check for common endpoints
        const expectedEndpoints = [
            '/health',
            '/auth/login',
            '/auth/profile',
            '/api/v1/demo',
            '/api/v1/data',
            '/api/v1/kol-feed/history'
        ];

        const missingEndpoints = expectedEndpoints.filter(endpoint => !content.includes(endpoint));
        
        if (missingEndpoints.length > 0) {
            console.warn('⚠️  Missing endpoints:', missingEndpoints.join(', '));
        }

        // Check for security schemes
        if (!content.includes('securitySchemes:')) {
            console.warn('⚠️  No security schemes defined');
        }

        // Basic syntax validation
        const lines = content.split('\n');
        let indentationErrors = 0;
        
        lines.forEach((line, index) => {
            if (line.trim() && line.match(/^\s*\t/)) {
                console.warn(`⚠️  Line ${index + 1}: Use spaces instead of tabs`);
                indentationErrors++;
            }
        });

        if (indentationErrors > 5) {
            console.warn('⚠️  Multiple indentation issues detected');
        }

        console.log('✅ OpenAPI specification validation passed!');
        console.log(`📄 File: ${OPENAPI_FILE}`);
        console.log(`📊 Size: ${(content.length / 1024).toFixed(1)}KB`);
        console.log(`📝 Lines: ${lines.length}`);
        
        return true;

    } catch (error) {
        console.error('❌ OpenAPI validation failed:', error.message);
        return false;
    }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const isValid = validateOpenAPISpec();
    process.exit(isValid ? 0 : 1);
}

export { validateOpenAPISpec };

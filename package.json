{"name": "nodejs-api", "version": "1.0.0", "description": "Robust API Engine with Credit-based Usage Tracking and WebSocket Support", "type": "module", "main": "app.js", "scripts": {"setup-check": "node tests/setup-check.js", "dev": "NODE_ENV=development node --watch app.js", "start": "NODE_ENV=production node app.js", "test": "NODE_ENV=test node --test", "test:api": "node tests/test.js", "test:websocket": "node tests/websocket-test.js", "db:migrate": "node src/scripts/migrate.js", "docs:sync": "node scripts/sync-readme.js", "docs:validate": "node scripts/validate-openapi.js", "docs:deploy": "node tools/readme/deploy-all.js", "docs:status": "node tools/readme/deploy-all.js status", "docs:openapi": "node tools/readme/sync-openapi.js", "docs:categories": "node tools/readme/manage-categories.js", "docs:pages": "node tools/readme/manage-docs.js", "docs:git-sync": "node tools/readme/git-sync.js cli", "docs:export": "node tools/readme/git-sync.js export", "docs:publish": "rdme openapi upload openapi.yaml --key=*************************************************************************** --slug=stalkapi-v1", "docs:validate-remote": "rdme openapi validate readme-export/openapi.yaml", "docs:update": "npm run docs:export && cp readme-export/openapi.yaml ./openapi.yaml && npm run docs:publish"}, "keywords": ["api", "websocket", "redis", "postgresql", "credit-system"], "author": "", "license": "ISC", "packageManager": "pnpm@10.10.0", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.16.0", "uuid": "^11.1.0", "ws": "^8.18.2"}}
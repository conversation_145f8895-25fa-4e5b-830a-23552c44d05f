import { WebSocketServer } from 'ws';
import { v4 as uuidv4 } from 'uuid';
import jwt from 'jsonwebtoken';
import { User } from '../models/User.js';
import { query } from '../config/database.js';
import { pubsub, cache } from '../config/redis.js';
import { getRealIP } from '../utils/ip.js';
import {
    checkWebSocketRateLimit,
    incrementWebSocketConnections,
    decrementWebSocketConnections
} from '../middleware/rateLimiter.js';
import { streamCreditManager, isStreamCreditsEnabled } from '../middleware/streamCredits.js';

export class WSServer {
    constructor(server) {
        this.wss = new WebSocketServer({
            server,
            path: '/ws'
        });
        
        this.clients = new Map(); // connectionId -> client info
        this.subscriptions = new Map(); // streamName -> Set of connectionIds
        this.userConnections = new Map(); // userId -> Set of connectionIds
        
        this.setupEventHandlers();
        this.setupRedisSubscriptions();
        this.startHeartbeat();
        
        console.log('✅ WebSocket server initialized');
    }

    // Authenticate WebSocket connection
    async authenticateConnection(req) {
        try {
            const url = new URL(req.url, 'http://localhost');
            const token = url.searchParams.get('token');
            const apiKey = url.searchParams.get('apiKey');

            if (!token && !apiKey) {
                console.log('No token or apiKey provided');
                return null;
            }

            let user = null;

            if (token) {
                try {
                    const decoded = jwt.verify(token, process.env.JWT_SECRET);
                    user = await User.findById(decoded.userId);
                } catch (error) {
                    console.log('JWT verification failed:', error);
                    return null;
                }
            } else if (apiKey) {
                user = await User.findByApiKey(apiKey);
                if (!user) console.log('No user found for apiKey:', apiKey);
            }

            if (!user || !user.is_active) {
                console.log('User not found or not active:', user);
                return null;
            }

            const rateLimitCheck = await checkWebSocketRateLimit(user.id, user);
            if (!rateLimitCheck.allowed) {
                console.log('Rate limit exceeded for user:', user.id);
                return null;
            }

            return user;
        } catch (error) {
            console.error('WebSocket authentication error:', error);
            return null;
        }
    }

    // Setup WebSocket event handlers
    setupEventHandlers() {
        this.wss.on('connection', async (ws, req) => {
            const connectionId = uuidv4();
            const sessionId = uuidv4();

            // Authenticate user
            const user = await this.authenticateConnection(req);
            if (!user) {
                ws.close(1008, 'Authentication failed');
                return;
            }
            
            // Store client information
            const clientInfo = {
                connectionId,
                sessionId,
                userId: user.id,
                user,
                ws,
                subscriptions: new Set(),
                lastActivity: Date.now(),
                ipAddress: getRealIP(req),
                userAgent: req.headers['user-agent']
            };
            
            this.clients.set(connectionId, clientInfo);
            
            // Track user connections
            if (!this.userConnections.has(user.id)) {
                this.userConnections.set(user.id, new Set());
            }
            this.userConnections.get(user.id).add(connectionId);
            
            // Increment connection count
            await incrementWebSocketConnections(user.id);
            
            // Store session in database
            await this.storeSession(clientInfo);
            
            console.log(`WebSocket connected: ${connectionId} (User: ${user.email})`);
            
            // Send welcome message
            this.sendMessage(ws, {
                type: 'connected',
                connectionId,
                sessionId,
                message: 'WebSocket connection established'
            });
            
            // Handle messages
            ws.on('message', (data) => this.handleMessage(connectionId, data));
            
            // Handle connection close
            ws.on('close', () => this.handleDisconnection(connectionId));
            
            // Handle errors
            ws.on('error', (error) => {
                console.error(`WebSocket error for ${connectionId}:`, error);
                this.handleDisconnection(connectionId);
            });
            
            // Update last activity
            ws.on('pong', () => {
                if (this.clients.has(connectionId)) {
                    this.clients.get(connectionId).lastActivity = Date.now();
                }
            });
        });
    }

    // Handle incoming messages
    async handleMessage(connectionId, data) {
        try {
            const client = this.clients.get(connectionId);
            if (!client) return;

            client.lastActivity = Date.now();

            let message;
            try {
                message = JSON.parse(data.toString());
                console.log(`📨 Received WebSocket message from ${connectionId}:`, JSON.stringify(message, null, 2));
            } catch (error) {
                console.error(`❌ Invalid JSON from ${connectionId}:`, data.toString());
                this.sendError(client.ws, 'Invalid JSON format');
                return;
            }

            const { type, payload } = message;
            
            switch (type) {
                case 'subscribe':
                    await this.handleSubscribe(client, payload);
                    break;
                    
                case 'unsubscribe':
                    await this.handleUnsubscribe(client, payload);
                    break;
                    
                case 'ping':
                    this.sendMessage(client.ws, { type: 'pong', timestamp: Date.now() });
                    break;
                    
                default:
                    this.sendError(client.ws, `Unknown message type: ${type}`);
            }
            
        } catch (error) {
            console.error('Error handling WebSocket message:', error);
            const client = this.clients.get(connectionId);
            if (client) {
                this.sendError(client.ws, 'Internal server error');
            }
        }
    }

    // Handle stream subscription
    async handleSubscribe(client, payload) {
        try {
            const { stream } = payload;
            
            if (!stream) {
                this.sendError(client.ws, 'Stream name is required');
                return;
            }
            
            // Check if user has access to this stream
            const hasAccess = await this.checkStreamAccess(client.user, stream);
            if (!hasAccess) {
                this.sendError(client.ws, `Access denied to stream: ${stream}`);
                return;
            }
            
            // Add to subscriptions
            if (!this.subscriptions.has(stream)) {
                this.subscriptions.set(stream, new Set());
            }
            
            this.subscriptions.get(stream).add(client.connectionId);
            client.subscriptions.add(stream);
            
            // Update database
            await this.updateSubscriptions(client);
            
            // Publish subscription event to Redis
            await pubsub.publish('stream_subscription', {
                type: 'subscribe',
                userId: client.userId,
                connectionId: client.connectionId,
                stream,
                timestamp: Date.now()
            });
            
            this.sendMessage(client.ws, {
                type: 'subscribed',
                stream,
                message: `Successfully subscribed to ${stream}`
            });
            
            console.log(`User ${client.user.email} subscribed to ${stream}`);
            
        } catch (error) {
            console.error('Error handling subscription:', error);
            this.sendError(client.ws, 'Subscription failed');
        }
    }

    // Handle stream unsubscription
    async handleUnsubscribe(client, payload) {
        try {
            const { stream } = payload;
            
            if (!stream) {
                this.sendError(client.ws, 'Stream name is required');
                return;
            }
            
            // Remove from subscriptions
            if (this.subscriptions.has(stream)) {
                this.subscriptions.get(stream).delete(client.connectionId);
                
                // Clean up empty stream subscriptions
                if (this.subscriptions.get(stream).size === 0) {
                    this.subscriptions.delete(stream);
                }
            }
            
            client.subscriptions.delete(stream);
            
            // Update database
            await this.updateSubscriptions(client);
            
            // Publish unsubscription event to Redis
            await pubsub.publish('stream_subscription', {
                type: 'unsubscribe',
                userId: client.userId,
                connectionId: client.connectionId,
                stream,
                timestamp: Date.now()
            });
            
            this.sendMessage(client.ws, {
                type: 'unsubscribed',
                stream,
                message: `Successfully unsubscribed from ${stream}`
            });
            
            console.log(`User ${client.user.email} unsubscribed from ${stream}`);
            
        } catch (error) {
            console.error('Error handling unsubscription:', error);
            this.sendError(client.ws, 'Unsubscription failed');
        }
    }

    // Handle client disconnection
    async handleDisconnection(connectionId) {
        try {
            const client = this.clients.get(connectionId);
            if (!client) return;
            
            console.log(`WebSocket disconnected: ${connectionId} (User: ${client.user.email})`);
            
            // Remove from all subscriptions
            for (const stream of client.subscriptions) {
                if (this.subscriptions.has(stream)) {
                    this.subscriptions.get(stream).delete(connectionId);
                    
                    // Clean up empty stream subscriptions
                    if (this.subscriptions.get(stream).size === 0) {
                        this.subscriptions.delete(stream);
                    }
                }
            }
            
            // Remove from user connections
            if (this.userConnections.has(client.userId)) {
                this.userConnections.get(client.userId).delete(connectionId);
                
                if (this.userConnections.get(client.userId).size === 0) {
                    this.userConnections.delete(client.userId);
                }
            }
            
            // Decrement connection count
            await decrementWebSocketConnections(client.userId);
            
            // Update database session
            await query(
                'UPDATE websocket_sessions SET disconnected_at = CURRENT_TIMESTAMP WHERE session_id = $1',
                [client.sessionId]
            );
            
            // Remove client
            this.clients.delete(connectionId);
            
        } catch (error) {
            console.error('Error handling disconnection:', error);
        }
    }

    // Check if user has access to stream
    async checkStreamAccess(user, streamName) {
        try {
            // Check if user has access to all streams (*)
            if (user.allowed_streams.includes('*')) {
                return true;
            }
            
            // Check if specific stream is allowed
            if (user.allowed_streams.includes(streamName)) {
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('Error checking stream access:', error);
            return false;
        }
    }

    // Store WebSocket session in database
    async storeSession(client) {
        try {
            await query(
                `INSERT INTO websocket_sessions (
                    session_id, user_id, connection_id, ip_address, user_agent
                ) VALUES ($1, $2, $3, $4, $5)`,
                [
                    client.sessionId,
                    client.userId,
                    client.connectionId,
                    client.ipAddress,
                    client.userAgent
                ]
            );
        } catch (error) {
            console.error('Error storing WebSocket session:', error);
        }
    }

    // Update subscriptions in database
    async updateSubscriptions(client) {
        try {
            const subscriptionsArray = Array.from(client.subscriptions);
            await query(
                'UPDATE websocket_sessions SET subscribed_streams = $1, last_activity = CURRENT_TIMESTAMP WHERE session_id = $2',
                [subscriptionsArray, client.sessionId]
            );
        } catch (error) {
            console.error('Error updating subscriptions:', error);
        }
    }

    // Setup Redis subscriptions for stream data
    setupRedisSubscriptions() {
        // Subscribe to all stream channels
        pubsub.subscribe('stream_data', (message) => {
            this.broadcastToStream(message.stream, message.data);
        });
        
        console.log('✅ Redis subscriptions setup for WebSocket');
    }

    // Broadcast message to all subscribers of a stream
    async broadcastToStream(streamName, data) {
        try {
            if (!this.subscriptions.has(streamName)) {
                return;
            }

            const subscribers = this.subscriptions.get(streamName);
            const message = {
                type: 'stream_data',
                stream: streamName,
                data,
                timestamp: Date.now()
            };

            // If stream credits are enabled, check and deduct credits
            if (isStreamCreditsEnabled()) {
                await this.broadcastWithCredits(streamName, message, subscribers);
            } else {
                // Original behavior - send to all subscribers without credit checks
                for (const connectionId of subscribers) {
                    const client = this.clients.get(connectionId);
                    if (client && client.ws.readyState === client.ws.OPEN) {
                        this.sendMessage(client.ws, message);
                    }
                }
            }

        } catch (error) {
            console.error('Error broadcasting to stream:', error);
        }
    }

    // Broadcast message with credit checking and deduction
    async broadcastWithCredits(streamName, message, subscribers) {
        try {
            // Get all user IDs for batch credit checking
            const userIds = [];
            const connectionToUser = new Map();

            for (const connectionId of subscribers) {
                const client = this.clients.get(connectionId);
                if (client && client.ws.readyState === client.ws.OPEN) {
                    userIds.push(client.userId);
                    connectionToUser.set(connectionId, client.userId);
                }
            }

            if (userIds.length === 0) {
                return;
            }

            // Batch check which users have sufficient credits
            const eligibleUsers = await streamCreditManager.batchCheckCredits(userIds, streamName);

            // Send messages and consume credits for eligible users
            const creditPromises = [];
            let sentCount = 0;
            let blockedCount = 0;

            for (const connectionId of subscribers) {
                const client = this.clients.get(connectionId);
                if (client && client.ws.readyState === client.ws.OPEN) {
                    const userId = client.userId;

                    if (eligibleUsers.has(userId)) {
                        // Send message
                        this.sendMessage(client.ws, message);
                        sentCount++;

                        // Queue credit consumption (don't await to avoid blocking)
                        creditPromises.push(
                            streamCreditManager.consumeStreamCredits(userId, streamName, connectionId)
                        );
                    } else {
                        // User doesn't have enough credits - send credit warning
                        this.sendMessage(client.ws, {
                            type: 'credit_warning',
                            stream: streamName,
                            message: 'Insufficient credits to receive stream data',
                            required_credits: streamCreditManager.getStreamCreditCost(streamName),
                            timestamp: Date.now()
                        });
                        blockedCount++;
                    }
                }
            }

            // Process credit consumption in background
            if (creditPromises.length > 0) {
                Promise.all(creditPromises).catch(error => {
                    console.error('❌ Error processing stream credit consumption:', error);
                });
            }

            if (blockedCount > 0) {
                console.log(`💳 Stream ${streamName}: sent to ${sentCount} users, blocked ${blockedCount} users (insufficient credits)`);
            }

        } catch (error) {
            console.error('❌ Error in broadcastWithCredits:', error);
            // Fallback to sending without credit checks
            for (const connectionId of subscribers) {
                const client = this.clients.get(connectionId);
                if (client && client.ws.readyState === client.ws.OPEN) {
                    this.sendMessage(client.ws, message);
                }
            }
        }
    }

    // Send message to WebSocket client
    sendMessage(ws, message) {
        try {
            if (ws.readyState === ws.OPEN) {
                ws.send(JSON.stringify(message));
            }
        } catch (error) {
            console.error('Error sending WebSocket message:', error);
        }
    }

    // Send error message to WebSocket client
    sendError(ws, error) {
        this.sendMessage(ws, {
            type: 'error',
            error,
            timestamp: Date.now()
        });
    }

    // Start heartbeat to check connection health
    startHeartbeat() {
        const interval = parseInt(process.env.WS_HEARTBEAT_INTERVAL) || 30000;
        
        setInterval(() => {
            const now = Date.now();
            const timeout = interval * 2; // 2x heartbeat interval
            
            for (const [connectionId, client] of this.clients) {
                if (now - client.lastActivity > timeout) {
                    console.log(`Closing inactive WebSocket connection: ${connectionId}`);
                    client.ws.terminate();
                    this.handleDisconnection(connectionId);
                } else if (client.ws.readyState === client.ws.OPEN) {
                    client.ws.ping();
                }
            }
        }, interval);
    }

    // Get connection statistics
    getStats() {
        return {
            totalConnections: this.clients.size,
            totalStreams: this.subscriptions.size,
            totalUsers: this.userConnections.size,
            streams: Array.from(this.subscriptions.keys()).map(stream => ({
                name: stream,
                subscribers: this.subscriptions.get(stream).size
            }))
        };
    }
}

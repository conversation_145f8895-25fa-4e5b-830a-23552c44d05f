import express from 'express';
import jwt from 'jsonwebtoken';
import { User } from '../models/User.js';
import { verifyToken } from '../middleware/auth.js';
import { createEndpointRateLimit } from '../middleware/rateLimiter.js';
import { getCreditStatus } from '../middleware/credits.js';

const router = express.Router();

// Rate limiting for auth endpoints - temporarily disabled for debugging
// const authRateLimit = createEndpointRateLimit(10, 15); // 10 requests per 15 minutes

// Register new user
router.post('/register', /* authRateLimit, */ async (req, res) => {
    try {
        const { email, password, tier_id } = req.body;
        
        // Validation
        if (!email || !password) {
            return res.status(400).json({
                error: 'Email and password are required'
            });
        }
        
        if (password.length < 8) {
            return res.status(400).json({
                error: 'Password must be at least 8 characters long'
            });
        }
        
        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                error: 'Invalid email format'
            });
        }
        
        // Create user
        const user = await User.create({
            email: email.toLowerCase(),
            password,
            tier_id: tier_id || 1 // Default to free tier
        });
        
        // Generate tokens
        const token = user.generateToken();
        const refreshToken = user.generateRefreshToken();
        
        res.status(201).json({
            message: 'User created successfully',
            user: user.toJSON(),
            token,
            refreshToken,
            apiKey: user.api_key
        });
        
    } catch (error) {
        console.error('Registration error:', error);
        
        if (error.message === 'User already exists with this email') {
            return res.status(409).json({ error: error.message });
        }
        
        res.status(500).json({
            error: 'Registration failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Login user
router.post('/login', /* authRateLimit, */ async (req, res) => {
    try {
        const { email, password } = req.body;
        
        if (!email || !password) {
            return res.status(400).json({
                error: 'Email and password are required'
            });
        }
        
        // Authenticate user
        const user = await User.authenticate(email.toLowerCase(), password);
        
        if (!user) {
            return res.status(401).json({
                error: 'Invalid email or password'
            });
        }
        
        if (!user.is_active) {
            return res.status(401).json({
                error: 'Account is deactivated'
            });
        }
        
        // Generate tokens
        const token = user.generateToken();
        const refreshToken = user.generateRefreshToken();
        
        res.json({
            message: 'Login successful',
            user: user.toJSON(),
            token,
            refreshToken
        });
        
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            error: 'Login failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Refresh token
router.post('/refresh', /* authRateLimit, */ async (req, res) => {
    try {
        const { refreshToken } = req.body;
        
        if (!refreshToken) {
            return res.status(400).json({
                error: 'Refresh token is required'
            });
        }
        
        // Verify refresh token
        const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET);
        
        if (decoded.type !== 'refresh') {
            return res.status(401).json({
                error: 'Invalid refresh token'
            });
        }
        
        // Get user
        const user = await User.findById(decoded.userId);
        
        if (!user || !user.is_active) {
            return res.status(401).json({
                error: 'User not found or inactive'
            });
        }
        
        // Generate new tokens
        const newToken = user.generateToken();
        const newRefreshToken = user.generateRefreshToken();
        
        res.json({
            token: newToken,
            refreshToken: newRefreshToken
        });
        
    } catch (error) {
        console.error('Token refresh error:', error);
        res.status(401).json({
            error: 'Invalid or expired refresh token'
        });
    }
});

// Get current user profile
router.get('/profile', verifyToken, async (req, res) => {
    try {
        const user = req.user;
        const creditStatus = await getCreditStatus(user.id);
        
        res.json({
            user: user,
            credits: creditStatus
        });
        
    } catch (error) {
        console.error('Profile fetch error:', error);
        res.status(500).json({
            error: 'Failed to fetch profile'
        });
    }
});

// Update user profile
router.put('/profile', verifyToken, async (req, res) => {
    try {
        const user = await User.findById(req.user.id);
        const { email } = req.body;
        
        const updateData = {};
        
        if (email && email !== user.email) {
            // Validate email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return res.status(400).json({
                    error: 'Invalid email format'
                });
            }
            updateData.email = email.toLowerCase();
        }
        
        if (Object.keys(updateData).length === 0) {
            return res.status(400).json({
                error: 'No valid fields to update'
            });
        }
        
        await user.update(updateData);
        
        res.json({
            message: 'Profile updated successfully',
            user: user.toJSON()
        });
        
    } catch (error) {
        console.error('Profile update error:', error);
        res.status(500).json({
            error: 'Failed to update profile'
        });
    }
});

// Change password
router.put('/password', verifyToken, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        
        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                error: 'Current password and new password are required'
            });
        }
        
        if (newPassword.length < 8) {
            return res.status(400).json({
                error: 'New password must be at least 8 characters long'
            });
        }
        
        // Verify current password
        const user = await User.authenticate(req.user.email, currentPassword);
        if (!user) {
            return res.status(401).json({
                error: 'Current password is incorrect'
            });
        }
        
        // Change password
        const success = await user.changePassword(newPassword);
        
        if (!success) {
            return res.status(500).json({
                error: 'Failed to change password'
            });
        }
        
        res.json({
            message: 'Password changed successfully'
        });
        
    } catch (error) {
        console.error('Password change error:', error);
        res.status(500).json({
            error: 'Failed to change password'
        });
    }
});

// Regenerate API key
router.post('/regenerate-api-key', verifyToken, async (req, res) => {
    try {
        const user = await User.findById(req.user.id);
        const newApiKey = await user.regenerateApiKey();
        
        if (!newApiKey) {
            return res.status(500).json({
                error: 'Failed to regenerate API key'
            });
        }
        
        res.json({
            message: 'API key regenerated successfully',
            apiKey: newApiKey
        });
        
    } catch (error) {
        console.error('API key regeneration error:', error);
        res.status(500).json({
            error: 'Failed to regenerate API key'
        });
    }
});

// Get usage statistics
router.get('/usage', verifyToken, async (req, res) => {
    try {
        const { days = 30 } = req.query;
        const user = await User.findById(req.user.id);
        const stats = await user.getUsageStats(parseInt(days));
        
        res.json({
            usage: stats,
            period: `${days} days`
        });
        
    } catch (error) {
        console.error('Usage stats error:', error);
        res.status(500).json({
            error: 'Failed to fetch usage statistics'
        });
    }
});

export default router;

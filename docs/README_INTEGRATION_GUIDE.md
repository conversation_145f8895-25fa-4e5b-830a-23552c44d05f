# Readme.com Integration Guide

This guide walks you through integrating your StalkAPI documentation with Readme.com for professional, interactive API documentation.

## 🚀 Quick Start

### Step 1: Create Readme.com Account

1. Go to [readme.com](https://readme.com) and sign up
2. Create a new project for StalkAPI
3. Choose a subdomain (e.g., `stalkapi.readme.io`)
4. Select the "API Documentation" template

### Step 2: Get Your API Key

1. Go to your Readme.com dashboard
2. Navigate to **Configuration** → **API Key**
3. Copy your API key

### Step 3: Configure Environment

```bash
# Copy the environment template
cp .env.readme.example .env.readme

# Edit with your values
nano .env.readme
```

Add your configuration:
```env
README_API_KEY=rdme_your_api_key_here
README_PROJECT_SLUG=stalkapi
README_VERSION=v1.0
```

### Step 4: Sync Documentation

```bash
# Validate OpenAPI spec first
npm run docs:validate

# Sync with Readme.com
npm run docs:sync
```

## 📖 Integration Options

### Option 1: OpenAPI/Swagger (Recommended)

**Pros:**
- ✅ Interactive API explorer
- ✅ Auto-generated code samples
- ✅ Request/response validation
- ✅ Easy maintenance

**Implementation:**
- We've created `docs/openapi.yaml` with your complete API spec
- Use `npm run docs:sync` to upload to Readme.com
- Readme.com will auto-generate beautiful interactive docs

### Option 2: Manual Documentation

**Pros:**
- ✅ Complete control over content
- ✅ Custom layouts and styling
- ✅ Rich markdown features

**Implementation:**
- Use Readme.com's web editor
- Copy content from your existing markdown files
- Organize into categories and sections

### Option 3: Hybrid Approach

**Best of both worlds:**
- Use OpenAPI for API reference
- Add custom pages for guides and tutorials
- Our sync script creates both automatically

## 🔧 Advanced Configuration

### Custom Domain Setup

1. In Readme.com dashboard, go to **Configuration** → **Custom Domain**
2. Add your domain (e.g., `docs.stalkapi.com`)
3. Configure DNS CNAME record:
   ```
   docs.stalkapi.com → cname.readme.io
   ```

### Branding Customization

1. **Logo**: Upload in **Configuration** → **Appearance**
2. **Colors**: Match your brand colors
3. **Favicon**: Add custom favicon
4. **CSS**: Add custom CSS for advanced styling

### Authentication Integration

Connect Readme.com with your API authentication:

```javascript
// Add to your Readme.com custom JavaScript
window.addEventListener('DOMContentLoaded', function() {
    // Auto-populate API key from user session
    if (window.userApiKey) {
        const apiKeyInputs = document.querySelectorAll('input[placeholder*="API"]');
        apiKeyInputs.forEach(input => {
            input.value = window.userApiKey;
        });
    }
});
```

## 📝 Content Organization

### Recommended Structure

```
📁 Getting Started
  📄 Quick Start
  📄 Authentication
  📄 Credit System

📁 API Reference
  📄 Authentication Endpoints
  📄 Core API Endpoints
  📄 KOL Feed Endpoints
  📄 WebSocket Streams

📁 Guides
  📄 WebSocket Integration
  📄 Error Handling
  📄 Rate Limiting
  📄 Best Practices

📁 SDKs & Tools
  📄 Postman Collection
  📄 Code Examples
  📄 Testing Guide
```

### Content Migration

Use our sync script to automatically create:

1. **Getting Started** - From `docs/SETUP_GUIDE.md`
2. **Credit System** - From `docs/CREDIT_SYSTEM_GUIDE.md`
3. **API Reference** - From `docs/openapi.yaml`
4. **KOL Feed Guide** - From `docs/KOL_FEED_GUIDE.md`

## 🔄 Automation & CI/CD

### GitHub Actions Integration

Create `.github/workflows/docs-sync.yml`:

```yaml
name: Sync Documentation

on:
  push:
    branches: [main]
    paths: ['docs/**']

jobs:
  sync-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm install
        
      - name: Sync with Readme.com
        env:
          README_API_KEY: ${{ secrets.README_API_KEY }}
          README_PROJECT_SLUG: stalkapi
        run: npm run docs:sync
```

### Webhook Integration

Set up webhooks to sync changes automatically:

1. In Readme.com, go to **Configuration** → **Webhooks**
2. Add webhook URL: `https://your-api.com/webhooks/readme`
3. Implement webhook handler in your API

## 📊 Analytics & Insights

### Built-in Analytics

Readme.com provides:
- Page views and user engagement
- Search queries and popular content
- API endpoint usage statistics
- User feedback and ratings

### Custom Analytics

Integrate with Google Analytics:

```javascript
// Add to Readme.com custom JavaScript
gtag('config', 'GA_MEASUREMENT_ID', {
  custom_map: {
    'custom_parameter_1': 'api_endpoint',
    'custom_parameter_2': 'user_tier'
  }
});
```

## 🎨 Customization Examples

### Custom CSS

```css
/* Add to Readme.com custom CSS */
.api-explorer {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.code-sample {
    background: #1a1a1a;
    border-radius: 6px;
}

.endpoint-badge {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}
```

### Interactive Examples

```javascript
// Add interactive API testing
function testEndpoint(endpoint, method = 'GET') {
    const apiKey = document.getElementById('api-key-input').value;
    
    fetch(`https://data.stalkapi.com${endpoint}`, {
        method: method,
        headers: {
            'X-API-Key': apiKey,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('response-output').textContent = 
            JSON.stringify(data, null, 2);
    });
}
```

## 🔍 SEO Optimization

### Meta Tags

Readme.com automatically generates SEO-friendly:
- Meta descriptions
- Open Graph tags
- Schema.org markup
- Sitemap.xml

### Custom SEO

Add custom meta tags in **Configuration** → **SEO**:

```html
<meta name="description" content="StalkAPI - Professional API for KOL trading data">
<meta name="keywords" content="API, KOL, trading, cryptocurrency, real-time">
<meta property="og:title" content="StalkAPI Documentation">
<meta property="og:description" content="Comprehensive API documentation for StalkAPI">
```

## 🚀 Go Live Checklist

- [ ] OpenAPI spec uploaded and validated
- [ ] Custom domain configured (optional)
- [ ] Branding and styling applied
- [ ] Content organized and reviewed
- [ ] Authentication examples tested
- [ ] Search functionality verified
- [ ] Analytics configured
- [ ] Team access permissions set
- [ ] Feedback collection enabled
- [ ] Mobile responsiveness checked

## 📞 Support & Resources

- **Readme.com Documentation**: [docs.readme.com](https://docs.readme.com)
- **OpenAPI Specification**: [swagger.io/specification](https://swagger.io/specification/)
- **Community Support**: [readme.com/community](https://readme.com/community)

## 🎯 Next Steps

1. **Run the sync script**: `npm run docs:sync`
2. **Visit your docs**: `https://your-project.readme.io`
3. **Customize appearance** in the Readme.com dashboard
4. **Add team members** and set permissions
5. **Configure custom domain** (optional)
6. **Set up analytics** and feedback collection

Your professional API documentation is now ready! 🎉

# StalkAPI Postman Collection Guide

This guide explains how to use the comprehensive Postman collection to test and interact with the StalkAPI engine, including REST API endpoints and WebSocket connection information.

## 📦 Collection Files

Located in the `postman/` folder:
- **`StalkAPI_Postman_Collection.json`** - Complete REST API collection with all endpoints
- **`StalkAPI_Postman_Environment.json`** - Environment variables for development and production
- **`README.md`** - Detailed collection documentation

## 🚀 Quick Setup

### 1. Import Collection and Environment

1. Open Postman
2. Click **Import** button
3. Import both files from the `postman/` folder:
   - `StalkAPI_Postman_Collection.json` (REST API)
   - `StalkAPI_Postman_Environment.json` (Environment)

### 2. Select Environment

1. In the top-right corner, select **"StalkAPI Environment"**
2. Verify the `base_url` is set correctly:
   - Development: `http://localhost:3001`
   - Production: `https://data.stalkapi.com`

### 3. Start Testing

1. Run the **"Login"** request first to get a JWT token
2. The token will be automatically saved to environment variables
3. All other requests will use this token automatically

## 📋 Collection Structure

### 🔐 Authentication

- **Login** - Get JWT token (auto-saves to environment)
- **Get Profile** - View user information and credits
- **Refresh Token** - Extend session

### 🌐 API Endpoints (JWT Auth)

- **Health Check** - Test API availability
- **Demo Endpoint** - Basic endpoint (all tiers, 1 credit)
- **Data Endpoint** - Data access (Basic+, 2 credits)
- **Analytics Endpoint** - Analytics data (Premium+, 5 credits)
- **Search Endpoint** - Search functionality (Basic+, 2 credits)
- **Submit Data** - Data submission (Basic+, 3 credits)
- **KOL Feed History** - Historical KOL trading data (Basic+, 3 credits)
- **Batch Processing** - Bulk operations (Enterprise only, 10 credits)

### 🔑 API Endpoints (API Key Auth)

- **Demo Endpoint (API Key)** - Using API key instead of JWT
- **Data Endpoint (API Key)** - Alternative authentication method

### 🔌 WebSocket API

- **WebSocket Info** - Get connection details
- **Available Streams** - List streams for user's tier

### 👑 Admin API (Enterprise Only)

- **Get All Tiers** - View all access tiers
- **Enable Tier** - Enable disabled tiers
- **Disable Tier** - Disable tiers (with safety checks)
- **Update Tier Configuration** - Modify tier settings
- **Get Tier Statistics** - View tier usage stats

## 🎯 Demo Credentials

The collection comes pre-configured with demo user credentials:

```
Email: <EMAIL>
Password: demo123
API Key: demo_api_key_12345
Tier: Basic (10,000 credits)
```

## 🔄 Workflow Examples

### Basic API Testing Workflow

1. **Login** → Get JWT token
2. **Get Profile** → Check credits and tier
3. **Demo Endpoint** → Test basic functionality
4. **Data Endpoint** → Test tier-based access
5. **Get Profile** → Verify credit consumption

### WebSocket Testing Workflow

1. **Login** → Get JWT token
2. **WebSocket Info** → Get connection details
3. **Available Streams** → See available streams
4. Use WebSocket client with: `ws://localhost:3001/ws?token={{jwt_token}}`

### Admin Testing Workflow (Enterprise Required)

1. **Login** with Enterprise user
2. **Get All Tiers** → View tier status
3. **Enable Tier** → Enable free tier
4. **Get Tier Statistics** → View usage stats
5. **Disable Tier** → Disable tier again

## 🌍 Environment Variables

### Automatic Variables

- `jwt_token` - Set automatically after login
- `user_id` - Set automatically after login

### Manual Variables

- `base_url` - API base URL
- `api_key` - Demo API key
- `websocket_url` - WebSocket connection URL

### Production Setup

To test against production:

1. Change `base_url` to `https://data.stalkapi.com`
2. Change `websocket_url` to `wss://data.stalkapi.com/ws`

## 📊 Understanding Responses

### Successful API Response

```json
{
  "success": true,
  "data": { ... },
  "credits_remaining": 9999,
  "credits_used": 1
}
```

### Error Response

```json
{
  "error": "Insufficient credits",
  "code": "INSUFFICIENT_CREDITS",
  "credits_remaining": 0
}
```

### Authentication Response

```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "tier": "basic",
    "credits_remaining": 10000
  }
}
```

## 🔧 Testing Different Scenarios

### Credit Consumption Testing

1. Check initial credits with **Get Profile**
2. Make API calls and watch credits decrease
3. Verify credit consumption matches endpoint costs

### Tier Access Testing

1. Try **Analytics Endpoint** with Basic tier (should fail)
2. Try **Demo Endpoint** with any tier (should work)
3. Check error messages for access denied

### Rate Limiting Testing

1. Make rapid requests to same endpoint
2. Observe rate limiting responses (429 status)
3. Wait and try again

### API Key vs JWT Testing

1. Use JWT auth endpoints
2. Use API key auth endpoints
3. Compare functionality and responses

## 🐛 Troubleshooting

### Common Issues

**"Unauthorized" Error**

- Check if JWT token is set in environment
- Try logging in again to refresh token
- Verify API key is correct for API key endpoints

**"Insufficient Credits" Error**

- Check credits with **Get Profile**
- This is expected behavior when credits run out

**"Access Denied" Error**

- Check user's tier with **Get Profile**
- Verify endpoint is available for user's tier

**Connection Refused**

- Ensure API server is running on correct port
- Check `base_url` in environment variables

### Debug Tips

1. **Check Environment Variables**

   - Click the eye icon next to environment dropdown
   - Verify all variables are set correctly

2. **View Request Details**

   - Check Headers tab for Authorization header
   - Verify request body format for POST requests

3. **Check Response**
   - Look at response status code
   - Read error messages in response body

## 📝 Custom Testing

### Adding New Requests

1. Right-click on folder → Add Request
2. Set method, URL, headers, and body
3. Use environment variables: `{{variable_name}}`

### Creating Test Scripts

Add to Tests tab:

```javascript
pm.test("Status code is 200", function () {
  pm.response.to.have.status(200);
});

pm.test("Response has success field", function () {
  const response = pm.response.json();
  pm.expect(response).to.have.property("success");
});
```

## 🔗 WebSocket Testing

For WebSocket testing, use external tools:

### Using wscat (Node.js)

```bash
npm install -g wscat
wscat -c "ws://localhost:3001/ws?token=YOUR_JWT_TOKEN"
```

### WebSocket Messages

```json
// Subscribe to stream
{
    "type": "subscribe",
    "payload": {
        "stream": "demo-stream"
    }
}

// Unsubscribe from stream
{
    "type": "unsubscribe",
    "payload": {
        "stream": "demo-stream"
    }
}

// Ping (keep alive)
{"type": "ping"}
```

This collection provides comprehensive testing capabilities for all StalkAPI features including authentication, tier-based access control, credit consumption, and real-time streaming.
